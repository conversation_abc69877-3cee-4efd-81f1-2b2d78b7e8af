# Task Completion Guidelines

## What to Do When Task is Completed

### 1. Code Quality Checks
Run linting and type checking commands to ensure code correctness:

**Python:**
```bash
# Check for any configured linting/formatting tools
# Look for these in package.json scripts or requirements.txt:
black .                    # Code formatting (if available)
flake8                     # Code linting (if available) 
mypy                       # Type checking (if available)
```

**JavaScript:**
```bash
npm run lint               # ESLint checking (available via .eslintrc.js)
```

### 2. Testing Requirements
**ALWAYS** run tests after completing a task:

**Python Tests:**
```bash
pytest tests/              # Run all Python tests
pytest tests/test_api.py -v  # Run API-specific tests
```

**JavaScript Tests:**
```bash
npx vitest                 # Run all JavaScript tests
npx vitest tests/simpleChart.test.ts  # Run specific tests
```

### 3. Documentation Updates
- Update `README.md` when new features are added
- Update `CLAUDE.md` if development commands change
- Comment non-obvious code with inline `# Reason:` explanations
- Update API documentation if endpoints change

### 4. Project-Specific Completion Tasks

**For TradingView API Features:**
- Test WebSocket connections manually
- Verify real-time data streaming works
- Check authentication with API keys
- Test both robust and basic Node.js services

**For FastAPI Endpoints:**
- Test via Swagger UI at http://localhost:8000/docs
- Verify authentication middleware works
- Check error handling for invalid inputs
- Test async operations complete properly

### 5. Never Commit Unless Explicitly Asked
- Do NOT run `git commit` unless user specifically requests it
- Users prefer to review changes before committing
- Use `git status` and `git diff` to show what changed

### 6. Verification Commands
After task completion, run these to verify everything works:

```bash
# Start services and test basic functionality
python start_server.py &
sleep 5
curl http://localhost:8000/health
curl http://localhost:3001/health

# Test authenticated endpoint
curl -H "Authorization: Bearer test-key" http://localhost:8000/api/marketdata/quote/BINANCE:BTCUSDT
```

### 7. Update Task Tracking
- Mark tasks as completed in `.clinerules/TASK.md`
- Add any discovered sub-tasks or TODOs to the task file
- Document any issues encountered during development