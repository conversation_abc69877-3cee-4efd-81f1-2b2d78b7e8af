/**
 * 🚀 WEBSOCKET-ONLY NODE.JS SERVICE - REVOLUTIONARY 429-PROOF SOLUTION 🚀
 * 
 * This service implements pure WebSocket streaming without any polling intervals.
 * TradingView pushes price updates automatically via native WebSocket events.
 * 
 * Key Features:
 * - ZERO polling - 100% push-based updates from TradingView
 * - Multi-connection architecture with intelligent load balancing  
 * - Revolutionary rate limiting with connection pooling
 * - Real-time broadcasting to multiple clients via Socket.IO
 * - Redis pub/sub for scalable real-time distribution
 * - Circuit breaker pattern for fault tolerance
 * - Health monitoring and automatic failover
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { io: socketClient } = require('socket.io-client');
const cors = require('cors');
const TradingView = require('./main');
const redis = require('redis');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Rate limiting and deduplication tracking for subscription storm prevention
const clientRateLimits = new Map(); // clientId -> { lastSubscribe: timestamp, subscribeCount: number, subscriptions: Set }
const subscriptionCooldown = 100; // 100ms cooldown between subscriptions per client
const maxSubscriptionsPerClient = 10; // Maximum active subscriptions per client
const maxSubscriptionsPerMinute = 30; // Maximum subscription attempts per minute per client

// Middleware
app.use(cors());
app.use(express.json());

// Configuration
const PORT = process.env.NODE_SERVICE_PORT || 3002;
const SESSION = process.env.TRADINGVIEW_SESSION;
const SIGNATURE = process.env.TRADINGVIEW_SIGNATURE;
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_PASSWORD = process.env.REDIS_PASSWORD;
const EXPRESS_BACKEND_URL = process.env.EXPRESS_BACKEND_URL || 'http://localhost:8000';

// 🚀 HYBRID ARCHITECTURE CONFIGURATION
const ENABLE_BACKEND_INTEGRATION = process.env.ENABLE_BACKEND_INTEGRATION === 'true';
const ENABLE_DIRECT_FRONTEND_ACCESS = process.env.ENABLE_DIRECT_FRONTEND_ACCESS !== 'false'; // Default true
const ENABLE_CHALLENGE_ENGINE_FEED = process.env.ENABLE_CHALLENGE_ENGINE_FEED === 'true';
const BACKEND_HEARTBEAT_INTERVAL = parseInt(process.env.BACKEND_HEARTBEAT_INTERVAL) || 30000;

// Challenge engine symbols (comma-separated string to array)
const CHALLENGE_ENGINE_SYMBOLS = (process.env.CHALLENGE_ENGINE_SYMBOLS || 
  'EURUSD,GBPUSD,USDJPY,USDCHF,AUDUSD,USDCAD,NZDUSD,BTCUSD,ETHUSD,ADAUSD,XRPUSD,SPX500,US30,NAS100,UK100,GOLD,SILVER'
).split(',').map(s => s.trim());

// Express Backend WebSocket Client
let expressBackendClient = null;

// Enhanced logging
const logger = {
    info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
    error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
    warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
    debug: (msg) => console.log(`[DEBUG] ${new Date().toISOString()} - ${msg}`)
};

// 🚀 GLOBAL REQUEST QUEUE - Eliminates 429 errors through centralized rate limiting
class TradingViewRequestQueue {
    constructor() {
        this.queue = [];
        this.processing = false;
        this.lastRequestTime = 0;
        this.minInterval = 200; // 200ms between requests (ultra-conservative for historical)
        this.maxConcurrentRequests = 1; // Only 1 concurrent request to TradingView
        this.activeRequests = 0;
        this.requestTimeout = 45000; // 45 second timeout for historical data
        this.backoffMultiplier = 1.5;
        this.maxBackoff = 5000;
        this.currentBackoff = 0;
        
        // Request deduplication cache
        this.recentRequests = new Map(); // key -> {promise, timestamp}
        this.cacheTimeout = 5000; // 5 second deduplication window
        
        logger.info('🎯 TradingView Request Queue initialized - Ultra-conservative rate limiting active');
    }
    
    async enqueue(requestType, symbol, requestFn) {
        // Check for duplicate recent requests
        const dedupeKey = `${requestType}:${symbol}`;
        const recent = this.recentRequests.get(dedupeKey);
        
        if (recent && Date.now() - recent.timestamp < this.cacheTimeout) {
            logger.debug(`🔄 Deduplicating request: ${dedupeKey}`);
            return recent.promise;
        }
        
        return new Promise((resolve, reject) => {
            const request = {
                type: requestType,
                symbol,
                fn: requestFn,
                resolve,
                reject,
                timestamp: Date.now(),
                retries: 0,
                maxRetries: 2
            };
            
            // Store in deduplication cache
            const promise = this._createCachedPromise(resolve, reject);
            this.recentRequests.set(dedupeKey, {
                promise,
                timestamp: Date.now()
            });
            
            // Clean up expired cache entries
            this._cleanupCache();
            
            this.queue.push(request);
            logger.debug(`📝 Queued ${requestType} request for ${symbol} (queue: ${this.queue.length})`);
            
            this._processQueue();
        });
    }
    
    _createCachedPromise(resolve, reject) {
        return new Promise((res, rej) => {
            // This promise will be resolved by the actual request processing
            setTimeout(() => {
                resolve = res;
                reject = rej;
            }, 0);
        });
    }
    
    _cleanupCache() {
        const now = Date.now();
        for (const [key, entry] of this.recentRequests.entries()) {
            if (now - entry.timestamp > this.cacheTimeout) {
                this.recentRequests.delete(key);
            }
        }
    }
    
    async _processQueue() {
        if (this.processing || this.queue.length === 0 || this.activeRequests >= this.maxConcurrentRequests) {
            return;
        }
        
        this.processing = true;
        
        while (this.queue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
            const timeSinceLastRequest = Date.now() - this.lastRequestTime;
            const waitTime = Math.max(0, this.minInterval + this.currentBackoff - timeSinceLastRequest);
            
            if (waitTime > 0) {
                logger.debug(`⏱️ Waiting ${waitTime}ms before next request (rate limiting)`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
            
            const request = this.queue.shift();
            this._executeRequest(request);
        }
        
        this.processing = false;
    }
    
    async _executeRequest(request) {
        this.activeRequests++;
        this.lastRequestTime = Date.now();
        
        logger.debug(`🚀 Executing ${request.type} request for ${request.symbol} (active: ${this.activeRequests})`);
        
        try {
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error(`Request timeout after ${this.requestTimeout}ms`)), this.requestTimeout)
            );
            
            const result = await Promise.race([request.fn(), timeoutPromise]);
            
            // Reset backoff on success
            this.currentBackoff = 0;
            
            request.resolve(result);
            logger.debug(`✅ Completed ${request.type} request for ${request.symbol}`);
            
        } catch (error) {
            logger.error(`❌ Request failed for ${request.symbol}: ${error.message}`);
            
            // Check if it's a 429 error and apply backoff
            if (error.message.includes('429') || error.message.includes('rate limit')) {
                this.currentBackoff = Math.min(
                    this.currentBackoff * this.backoffMultiplier + 1000,
                    this.maxBackoff
                );
                logger.warn(`🐌 Rate limit detected - backing off by ${this.currentBackoff}ms`);
            }
            
            // Retry logic
            if (request.retries < request.maxRetries && 
                (error.message.includes('429') || error.message.includes('timeout'))) {
                request.retries++;
                logger.info(`🔄 Retrying ${request.type} for ${request.symbol} (attempt ${request.retries + 1})`);
                
                // Re-queue with exponential backoff
                setTimeout(() => {
                    this.queue.unshift(request);
                    this._processQueue();
                }, Math.pow(2, request.retries) * 1000);
            } else {
                request.reject(error);
            }
        } finally {
            this.activeRequests--;
            
            // Continue processing queue
            setTimeout(() => this._processQueue(), 50);
        }
    }
    
    getStats() {
        return {
            queueLength: this.queue.length,
            activeRequests: this.activeRequests,
            currentBackoff: this.currentBackoff,
            cacheSize: this.recentRequests.size,
            lastRequestTime: this.lastRequestTime
        };
    }
}

// Global request queue instance
const requestQueue = new TradingViewRequestQueue();

// Redis client for real-time caching and pub/sub
let redisClient = null;
let redisPublisher = null;

// 🎯 REVOLUTIONARY MULTI-CONNECTION ARCHITECTURE
class WebSocketConnectionPool {
    constructor() {
        this.connections = new Map(); // connectionId -> TradingViewClient
        this.subscriptions = new Map(); // symbol -> Set<connectionId>
        this.symbolClients = new Map(); // symbol -> connectionId (load balancing)
        this.clientSymbols = new Map(); // connectionId -> Set<symbols>
        this.connectionHealth = new Map(); // connectionId -> health status
        this.maxConnectionsPerClient = 50; // Conservative limit per TradingView client
        this.totalConnections = 0;
        this.circuitBreaker = {
            failures: 0,
            rateLimitFailures: 0, // Track 429-specific failures
            lastFailureTime: null,
            lastRateLimitTime: null,
            state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
            failureThreshold: 3, // Reduced threshold for faster response
            rateLimitThreshold: 2, // Special threshold for 429 errors
            recoveryTimeout: 45000, // 45 seconds recovery
            rateLimitRecoveryTimeout: 60000 // 60 seconds for rate limit recovery
        };
        
        logger.info('🚀 WebSocket Connection Pool initialized');
    }
    
    async initialize() {
        try {
            // Initialize Redis connections with enhanced authentication handling
            await this.initializeRedisConnections();
            
            // Create initial connection pool
            await this.createInitialConnections();
            
        } catch (error) {
            logger.error(`❌ Failed to initialize WebSocket pool: ${error.message}`);
            throw error;
        }
    }
    
    async initializeRedisConnections() {
        try {
            let redisConfig;
            
            if (REDIS_PASSWORD) {
                // Use authenticated Redis URL format when password is provided
                redisConfig = {
                    url: REDIS_URL,
                    password: REDIS_PASSWORD,
                    socket: {
                        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
                    }
                };
                logger.info('🔐 Using Redis authentication with password');
            } else {
                // Use simple URL format when no password
                redisConfig = { 
                    url: REDIS_URL,
                    socket: {
                        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
                    }
                };
                logger.info('🔓 Using Redis without authentication');
            }
            
            redisClient = redis.createClient(redisConfig);
            redisPublisher = redis.createClient(redisConfig);
            
            // Enhanced error handlers with fallback mode
            redisClient.on('error', (err) => {
                logger.error(`❌ Redis client error: ${err.message}`);
                if (err.message.includes('NOAUTH') || err.message.includes('AUTH')) {
                    logger.warn('🔧 Redis authentication failed - switching to no-cache mode');
                    redisClient = null; // Disable Redis caching
                }
            });
            
            redisPublisher.on('error', (err) => {
                logger.error(`❌ Redis publisher error: ${err.message}`);
                if (err.message.includes('NOAUTH') || err.message.includes('AUTH')) {
                    logger.warn('🔧 Redis publisher auth failed - disabling pub/sub');
                    redisPublisher = null; // Disable Redis pub/sub
                }
            });
            
            // Connection handlers
            redisClient.on('connect', () => {
                logger.info('🔗 Redis client connected');
            });
            
            redisPublisher.on('connect', () => {
                logger.info('🔗 Redis publisher connected');
            });
            
            redisClient.on('ready', () => {
                logger.info('✅ Redis client ready for commands');
            });
            
            redisPublisher.on('ready', () => {
                logger.info('✅ Redis publisher ready for commands');
            });
            
            // Attempt connections with timeout
            const connectTimeout = 5000; // 5 seconds timeout
            
            try {
                await Promise.race([
                    redisClient.connect(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Redis client connection timeout')), connectTimeout)
                    )
                ]);
            } catch (clientError) {
                logger.warn(`⚠️ Redis client connection failed: ${clientError.message} - disabling caching`);
                redisClient = null;
            }
            
            try {
                await Promise.race([
                    redisPublisher.connect(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Redis publisher connection timeout')), connectTimeout)
                    )
                ]);
            } catch (publisherError) {
                logger.warn(`⚠️ Redis publisher connection failed: ${publisherError.message} - disabling pub/sub`);
                redisPublisher = null;
            }
            
            if (redisClient && redisPublisher) {
                logger.info('✅ Redis connections established for WebSocket pool');
            } else if (redisClient || redisPublisher) {
                logger.warn('⚠️ Partial Redis connection established - limited functionality');
            } else {
                logger.warn('⚠️ Redis unavailable - running in no-cache mode (WebSocket functionality still works)');
            }
            
        } catch (error) {
            logger.warn(`⚠️ Redis initialization failed: ${error.message} - continuing without Redis`);
            redisClient = null;
            redisPublisher = null;
        }
    }
    
    async createInitialConnections() {
        // Start with 3 connections for load distribution
        const initialConnections = 3;
        
        for (let i = 0; i < initialConnections; i++) {
            await this.createNewConnection(`pool_${i}`);
        }
        
        logger.info(`🔗 Created ${initialConnections} initial TradingView connections`);
    }
    
    async createNewConnection(connectionId) {
        try {
            // Enhanced circuit breaker with 429-specific logic
            if (this.circuitBreaker.state === 'OPEN') {
                const timeSinceFailure = Date.now() - this.circuitBreaker.lastFailureTime;
                const timeSinceRateLimit = Date.now() - (this.circuitBreaker.lastRateLimitTime || 0);
                const requiredTimeout = this.circuitBreaker.rateLimitFailures > 0 ? 
                    this.circuitBreaker.rateLimitRecoveryTimeout : this.circuitBreaker.recoveryTimeout;
                
                if (timeSinceFailure < requiredTimeout || timeSinceRateLimit < this.circuitBreaker.rateLimitRecoveryTimeout) {
                    throw new Error(`Circuit breaker is OPEN - preventing new connections (rate limit cooldown: ${Math.ceil((requiredTimeout - timeSinceFailure) / 1000)}s)`);
                } else {
                    this.circuitBreaker.state = 'HALF_OPEN';
                    logger.info('🔄 Circuit breaker entering HALF_OPEN state after cooldown');
                }
            }
            
            const client = this.createClient();
            
            // Enhanced connection health monitoring
            this.connectionHealth.set(connectionId, {
                status: 'connecting',
                lastHeartbeat: Date.now(),
                subscriptionCount: 0,
                errorCount: 0,
                rateLimitErrors: 0, // Track 429 errors specifically
                successfulRequests: 0,
                lastError: null,
                lastRateLimitError: null,
                healthScore: 100, // 0-100 health score
                createdAt: Date.now()
            });
            
            this.connections.set(connectionId, client);
            this.clientSymbols.set(connectionId, new Set());
            this.totalConnections++;
            
            logger.info(`✅ Created TradingView connection: ${connectionId} (Total: ${this.totalConnections})`);
            
            // Reset circuit breaker on successful connection
            if (this.circuitBreaker.state === 'HALF_OPEN') {
                this.circuitBreaker.state = 'CLOSED';
                this.circuitBreaker.failures = 0;
                this.circuitBreaker.rateLimitFailures = 0;
                logger.info('✅ Circuit breaker reset to CLOSED state - all counters cleared');
            } else if (this.circuitBreaker.state === 'CLOSED') {
                // Gradually reduce failure counts on successful operations
                this.circuitBreaker.failures = Math.max(0, this.circuitBreaker.failures - 1);
                this.circuitBreaker.rateLimitFailures = Math.max(0, this.circuitBreaker.rateLimitFailures - 1);
            }
            
            return client;
            
        } catch (error) {
            logger.error(`❌ Failed to create connection ${connectionId}: ${error.message}`);
            
            // Enhanced circuit breaker with 429-specific handling
            this.circuitBreaker.failures++;
            this.circuitBreaker.lastFailureTime = Date.now();
            
            // Check if it's a 429 rate limit error
            if (error.message.includes('429') || error.message.includes('rate limit')) {
                this.circuitBreaker.rateLimitFailures++;
                this.circuitBreaker.lastRateLimitTime = Date.now();
                logger.error(`⏰ Rate limit failure #${this.circuitBreaker.rateLimitFailures} detected`);
                
                // Open circuit breaker immediately on rate limit
                if (this.circuitBreaker.rateLimitFailures >= this.circuitBreaker.rateLimitThreshold) {
                    this.circuitBreaker.state = 'OPEN';
                    logger.error(`🚨 Circuit breaker OPENED due to rate limiting (${this.circuitBreaker.rateLimitFailures} rate limit failures)`);
                }
            } else if (this.circuitBreaker.failures >= this.circuitBreaker.failureThreshold) {
                this.circuitBreaker.state = 'OPEN';
                logger.error(`🚨 Circuit breaker OPENED after ${this.circuitBreaker.failures} general failures`);
            }
            
            throw error;
        }
    }
    
    createClient() {
        if (SESSION && SIGNATURE) {
            logger.debug('Creating authenticated TradingView client');
            return new TradingView.Client({
                token: SESSION,
                signature: SIGNATURE
            });
        } else {
            logger.debug('Creating unauthenticated TradingView client');
            return new TradingView.Client();
        }
    }
    
    async subscribeToSymbol(symbol, socketId, callback) {
        try {
            // Check if already subscribed to prevent duplicate WebSocket connections
            if (this.subscriptions.has(symbol)) {
                const existingSubscriptions = this.subscriptions.get(symbol);
                if (existingSubscriptions.has(socketId)) {
                    logger.warn(`🚨 Duplicate subscription prevented: ${socketId} already subscribed to ${symbol}`);
                    return { duplicate: true };
                }
                // Add to existing subscription without creating new connection
                existingSubscriptions.add(socketId);
                logger.info(`🔗 Added ${socketId} to existing ${symbol} subscription`);
                return { added: true };
            }
            
            // Get or create optimal connection for this symbol
            let connectionId = this.symbolClients.get(symbol);
            
            if (!connectionId || !this.connections.has(connectionId)) {
                connectionId = await this.getOptimalConnection(symbol);
            }
            
            const client = this.connections.get(connectionId);
            if (!client) {
                throw new Error(`No available connection for symbol ${symbol}`);
            }
            
            // Create quote session and market subscription
            const quoteSession = new client.Session.Quote({ fields: 'all' });
            const market = new quoteSession.Market(symbol);
            
            // 🎯 PURE WEBSOCKET EVENT-DRIVEN UPDATES (NO POLLING!)
            market.onData((quoteData) => {
                const timestamp = new Date().toISOString();
                const marketData = {
                    symbol,
                    data: quoteData,
                    timestamp,
                    source: 'tradingview_websocket',
                    connectionId,
                    socketId
                };
                
                // Store in Redis cache
                this.cacheMarketData(symbol, marketData);
                
                // Publish to Redis pub/sub
                this.publishMarketData(symbol, marketData);
                
                // Direct callback to client
                callback(marketData);
                
                logger.debug(`📡 WebSocket update: ${symbol} = ${quoteData.lp || 'N/A'}`);
            });
            
            market.onLoaded(() => {
                logger.info(`✅ Market loaded: ${symbol} on connection ${connectionId}`);
            });
            
            market.onError((...errorArgs) => {
                const errorMessage = errorArgs.join(' ');
                const error = new Error(errorMessage);
                logger.error(`❌ Market error for ${symbol}:`, errorMessage);
                
                const health = this.connectionHealth.get(connectionId);
                if (health) {
                    health.errorCount++;
                }
                
                // Try reconnection on error with proper error object
                this.handleConnectionError(connectionId, symbol, error);
            });
            
            // Track subscription
            if (!this.subscriptions.has(symbol)) {
                this.subscriptions.set(symbol, new Set());
            }
            this.subscriptions.get(symbol).add(socketId);
            this.clientSymbols.get(connectionId).add(symbol);
            this.symbolClients.set(symbol, connectionId);
            
            // Update connection health with success tracking
            const health = this.connectionHealth.get(connectionId);
            health.subscriptionCount++;
            health.lastHeartbeat = Date.now();
            health.status = 'active';
            health.successfulRequests++;
            
            // Improve health score on successful operations
            if (health.errorCount > 0) {
                const totalRequests = health.successfulRequests + health.errorCount;
                health.healthScore = Math.min(100, Math.floor(
                    ((health.successfulRequests - health.rateLimitErrors * 2) / totalRequests) * 100
                ));
            }
            
            logger.info(`🔗 Subscribed ${socketId} to ${symbol} via connection ${connectionId}`);
            
            return { market, quoteSession, connectionId };
            
        } catch (error) {
            logger.error(`💥 Failed to subscribe to ${symbol}: ${error.message}`);
            throw error;
        }
    }
    
    async getOptimalConnection(symbol) {
        // Find connection with best health score and lowest load
        let optimalConnectionId = null;
        let bestScore = -1;
        
        for (const [connectionId, symbolSet] of this.clientSymbols.entries()) {
            const health = this.connectionHealth.get(connectionId);
            
            // Skip unhealthy connections
            if (!health || health.status === 'error' || health.status === 'degraded') {
                continue;
            }
            
            // Skip connections that hit rate limits recently (within 60 seconds)
            if (health.lastRateLimitError && (Date.now() - health.lastRateLimitError) < 60000) {
                logger.debug(`⏰ Skipping ${connectionId} due to recent rate limit (${Math.ceil((60000 - (Date.now() - health.lastRateLimitError)) / 1000)}s ago)`);
                continue;
            }
            
            // Calculate composite score: health score + load factor
            if (symbolSet.size < this.maxConnectionsPerClient) {
                const loadFactor = (this.maxConnectionsPerClient - symbolSet.size) / this.maxConnectionsPerClient * 100;
                const compositeScore = (health.healthScore * 0.7) + (loadFactor * 0.3);
                
                if (compositeScore > bestScore) {
                    bestScore = compositeScore;
                    optimalConnectionId = connectionId;
                }
            }
        }
        
        // Create new connection if needed
        if (!optimalConnectionId) {
            const newConnectionId = `pool_${Date.now()}`;
            await this.createNewConnection(newConnectionId);
            optimalConnectionId = newConnectionId;
        }
        
        if (optimalConnectionId) {
            const health = this.connectionHealth.get(optimalConnectionId);
            const load = this.clientSymbols.get(optimalConnectionId)?.size || 0;
            logger.debug(`🎯 Optimal connection for ${symbol}: ${optimalConnectionId} (health: ${health?.healthScore || 'unknown'}, load: ${load}, score: ${bestScore.toFixed(1)})`);
        } else {
            logger.debug(`⚠️ No optimal connection found for ${symbol} - will create new connection`);
        }
        return optimalConnectionId;
    }
    
    async handleConnectionError(connectionId, symbol, error = null) {
        logger.warn(`🔄 Handling connection error for ${connectionId}, symbol: ${symbol}`);
        
        try {
            // Enhanced connection health tracking
            const health = this.connectionHealth.get(connectionId);
            if (health) {
                health.status = 'error';
                health.errorCount++;
                health.lastError = error?.message || 'Unknown error';
                
                // Track 429 errors specifically
                if (error?.message?.includes('429') || error?.message?.includes('rate limit')) {
                    health.rateLimitErrors++;
                    health.lastRateLimitError = Date.now();
                    logger.error(`⏰ Connection ${connectionId} hit rate limit (total: ${health.rateLimitErrors})`);
                }
                
                // Calculate health score (0-100)
                const totalRequests = health.successfulRequests + health.errorCount;
                if (totalRequests > 0) {
                    health.healthScore = Math.max(0, Math.floor(
                        ((health.successfulRequests - health.rateLimitErrors * 2) / totalRequests) * 100
                    ));
                }
                
                // Mark connection as degraded if too many rate limit errors
                if (health.rateLimitErrors >= 3) {
                    health.status = 'degraded';
                    logger.warn(`⚠️ Connection ${connectionId} marked as degraded due to ${health.rateLimitErrors} rate limit errors`);
                }
            }
            
            // Try to reassign symbol to different connection
            const newConnectionId = await this.getOptimalConnection(symbol);
            if (newConnectionId !== connectionId) {
                this.symbolClients.set(symbol, newConnectionId);
                logger.info(`🔄 Reassigned ${symbol} from ${connectionId} to ${newConnectionId}`);
            }
            
        } catch (error) {
            logger.error(`💥 Error handling connection failure: ${error.message}`);
        }
    }
    
    async cacheMarketData(symbol, data) {
        if (!redisClient) {
            logger.debug(`📝 Redis unavailable - skipping cache for ${symbol}`);
            return;
        }
        
        try {
            const key = `websocket:market_data:${symbol}`;
            const serializedData = JSON.stringify(data);
            
            // Cache with 5-minute TTL
            await redisClient.setEx(key, 300, serializedData);
            
            // Store last price for quick access
            const priceKey = `websocket:last_price:${symbol}`;
            const priceData = {
                symbol,
                price: data.data?.lp || null,
                timestamp: data.timestamp
            };
            await redisClient.setEx(priceKey, 600, JSON.stringify(priceData));
            
            logger.debug(`💾 Cached market data for ${symbol}`);
            
        } catch (error) {
            logger.error(`Failed to cache data for ${symbol}: ${error.message}`);
            // Disable Redis client if persistent errors
            if (error.message.includes('NOAUTH') || error.message.includes('connection')) {
                logger.warn('🔧 Disabling Redis client due to persistent errors');
                redisClient = null;
            }
        }
    }
    
    async publishMarketData(symbol, data) {
        // Always forward to Express backend regardless of Redis status
        this.forwardToExpressBackend(symbol, data);
        
        if (!redisPublisher) {
            logger.debug(`📡 Redis pub/sub unavailable - skipping publish for ${symbol}`);
            return;
        }
        
        try {
            await redisPublisher.publish(`websocket:market_data:${symbol}`, JSON.stringify(data));
            logger.debug(`📡 Published market data for ${symbol} to Redis`);
            
        } catch (error) {
            logger.error(`Failed to publish data for ${symbol}: ${error.message}`);
            // Disable Redis publisher if persistent errors
            if (error.message.includes('NOAUTH') || error.message.includes('connection')) {
                logger.warn('🔧 Disabling Redis publisher due to persistent errors');
                redisPublisher = null;
            }
        }
    }
    
    forwardToExpressBackend(symbol, data) {
        if (expressBackendClient && expressBackendClient.connected) {
            try {
                // Send market data to Express backend with standardized format
                expressBackendClient.emit('revolutionary_market_data', {
                    symbol,
                    price: data.data?.lp || data.data?.price || null,
                    bid: data.data?.bid || null,
                    ask: data.data?.ask || null,
                    change: data.data?.ch || data.data?.change || null,
                    changePercent: data.data?.chp || data.data?.changePercent || null,
                    volume: data.data?.volume || null,
                    timestamp: data.timestamp,
                    source: 'revolutionary_websocket',
                    connectionId: data.connectionId,
                    raw: data.data
                });
                
                logger.debug(`📤 Forwarded ${symbol} data to Express backend: ${data.data?.lp || 'N/A'}`);
            } catch (error) {
                logger.error(`Failed to forward ${symbol} data to Express backend: ${error.message}`);
            }
        }
    }
    
    unsubscribeFromSymbol(symbol, socketId) {
        try {
            const subscriptions = this.subscriptions.get(symbol);
            if (subscriptions) {
                subscriptions.delete(socketId);
                
                // Remove symbol mapping if no more subscribers
                if (subscriptions.size === 0) {
                    this.subscriptions.delete(symbol);
                    
                    const connectionId = this.symbolClients.get(symbol);
                    if (connectionId) {
                        this.clientSymbols.get(connectionId)?.delete(symbol);
                        this.symbolClients.delete(symbol);
                        
                        // Update connection health
                        const health = this.connectionHealth.get(connectionId);
                        if (health) {
                            health.subscriptionCount--;
                        }
                    }
                }
            }
            
            logger.info(`🔌 Unsubscribed ${socketId} from ${symbol}`);
            
        } catch (error) {
            logger.error(`Error unsubscribing from ${symbol}: ${error.message}`);
        }
    }
    
    getPoolStats() {
        // Calculate aggregate health metrics
        let totalHealthScore = 0;
        let healthyConnections = 0;
        let degradedConnections = 0;
        let errorConnections = 0;
        let totalRateLimitErrors = 0;
        
        for (const [_, health] of this.connectionHealth.entries()) {
            totalHealthScore += health.healthScore || 0;
            totalRateLimitErrors += health.rateLimitErrors || 0;
            
            switch (health.status) {
                case 'active':
                    healthyConnections++;
                    break;
                case 'degraded':
                    degradedConnections++;
                    break;
                case 'error':
                    errorConnections++;
                    break;
            }
        }
        
        const stats = {
            totalConnections: this.totalConnections,
            totalSubscriptions: this.subscriptions.size,
            connectionHealth: Object.fromEntries(this.connectionHealth),
            circuitBreaker: this.circuitBreaker,
            symbolDistribution: {},
            aggregateHealth: {
                averageHealthScore: this.totalConnections > 0 ? Math.round(totalHealthScore / this.totalConnections) : 0,
                healthyConnections,
                degradedConnections,
                errorConnections,
                totalRateLimitErrors
            },
            requestQueue: requestQueue.getStats(),
            systemStatus: this._getSystemStatus()
        };
        
        // Calculate symbol distribution across connections
        for (const [connectionId, symbolSet] of this.clientSymbols.entries()) {
            stats.symbolDistribution[connectionId] = symbolSet.size;
        }
        
        return stats;
    }
    
    _getSystemStatus() {
        const { averageHealthScore, healthyConnections, degradedConnections, errorConnections } = this.getPoolStats().aggregateHealth || {};
        const requestQueueHealth = requestQueue.getStats();
        
        if (this.circuitBreaker.state === 'OPEN') {
            return 'CIRCUIT_BREAKER_OPEN';
        } else if (requestQueueHealth.currentBackoff > 1000) {
            return 'RATE_LIMITED';
        } else if (errorConnections > healthyConnections) {
            return 'DEGRADED';
        } else if (averageHealthScore > 80 && healthyConnections > 0) {
            return 'HEALTHY';
        } else {
            return 'WARNING';
        }
    }
}

// Initialize connection pool
const wsPool = new WebSocketConnectionPool();

// 🔗 EXPRESS BACKEND CONNECTION MANAGER
async function initializeExpressBackendConnection() {
    if (!ENABLE_BACKEND_INTEGRATION) {
        logger.info('📴 Backend integration disabled - skipping Express connection');
        return null;
    }
    
    try {
        logger.info(`🔗 Connecting to Express backend at ${EXPRESS_BACKEND_URL}...`);
        
        expressBackendClient = socketClient(EXPRESS_BACKEND_URL, {
            transports: ['websocket', 'polling'],
            upgrade: true,
            rememberUpgrade: true,
            autoConnect: true,
            reconnection: true,
            reconnectionDelay: 2000,
            reconnectionAttempts: 10,
            timeout: 10000
        });
        
        expressBackendClient.on('connect', () => {
            logger.info('✅ Connected to Express backend - Revolutionary data flow active!');
            
            // Send initial connection message
            expressBackendClient.emit('revolutionary_service_connected', {
                service: 'websocket_only',
                version: '1.0.0',
                capabilities: [
                    'real_time_streaming',
                    'multi_connection_pooling', 
                    'circuit_breaker_protection',
                    'redis_pub_sub'
                ],
                timestamp: new Date().toISOString()
            });
        });
        
        expressBackendClient.on('disconnect', (reason) => {
            logger.warn(`🔌 Disconnected from Express backend: ${reason}`);
        });
        
        expressBackendClient.on('connect_error', (error) => {
            logger.error(`❌ Express backend connection error: ${error.message}`);
        });
        
        expressBackendClient.on('reconnect', (attemptNumber) => {
            logger.info(`🔄 Reconnected to Express backend (attempt ${attemptNumber})`);
        });
        
        // Handle backend requests for specific symbols
        expressBackendClient.on('request_symbol_subscription', (data) => {
            const { symbol, socketId } = data;
            logger.info(`📡 Express backend requested subscription for ${symbol}`);
            
            // Subscribe to symbol if not already subscribed
            wsPool.subscribeToSymbol(symbol, `backend_${socketId}`, (marketData) => {
                // Data will be automatically forwarded via publishMarketData
            }).catch(error => {
                logger.error(`Failed to subscribe to ${symbol} for backend: ${error.message}`);
            });
        });
        
        return expressBackendClient;
        
    } catch (error) {
        logger.error(`💥 Failed to initialize Express backend connection: ${error.message}`);
        throw error;
    }
}

// Client WebSocket subscriptions tracking
const clientSubscriptions = new Map(); // socketId -> Set<symbols>

// Enhanced health check endpoint
app.get('/health', (req, res) => {
    const stats = wsPool.getPoolStats();
    const requestQueueStats = requestQueue.getStats();
    const systemStatus = stats.systemStatus;
    
    // Determine HTTP status code based on system health
    let httpStatus = 200;
    if (systemStatus === 'CIRCUIT_BREAKER_OPEN' || systemStatus === 'DEGRADED') {
        httpStatus = 503; // Service Unavailable
    } else if (systemStatus === 'RATE_LIMITED' || systemStatus === 'WARNING') {
        httpStatus = 429; // Too Many Requests or Warning
    }
    
    res.status(httpStatus).json({ 
        status: systemStatus.toLowerCase(),
        healthy: systemStatus === 'HEALTHY',
        timestamp: new Date().toISOString(),
        service: 'revolutionary-websocket-v2',
        architecture: 'zero-polling-websocket-only',
        version: '2.0.0',
        configuration: {
            directFrontendAccess: ENABLE_DIRECT_FRONTEND_ACCESS,
            backendIntegration: ENABLE_BACKEND_INTEGRATION,
            challengeEngineFeed: ENABLE_CHALLENGE_ENGINE_FEED,
            challengeEngineSymbols: CHALLENGE_ENGINE_SYMBOLS.length,
            expressBackendConnected: expressBackendClient?.connected || false,
            requestQueueEnabled: true,
            circuitBreakerEnabled: true
        },
        metrics: {
            totalConnections: stats.totalConnections,
            totalSubscriptions: stats.totalSubscriptions,
            averageHealthScore: stats.aggregateHealth.averageHealthScore,
            healthyConnections: stats.aggregateHealth.healthyConnections,
            degradedConnections: stats.aggregateHealth.degradedConnections,
            errorConnections: stats.aggregateHealth.errorConnections,
            totalRateLimitErrors: stats.aggregateHealth.totalRateLimitErrors,
            queueLength: requestQueueStats.queueLength,
            activeRequests: requestQueueStats.activeRequests,
            currentBackoff: requestQueueStats.currentBackoff,
            circuitBreakerState: stats.circuitBreaker.state,
            rateLimitFailures: stats.circuitBreaker.rateLimitFailures
        },
        poolStats: req.query.detailed ? stats : undefined
    });
});

// Enhanced pool statistics endpoint
app.get('/api/pool/stats', (req, res) => {
    const stats = wsPool.getPoolStats();
    const requestQueueStats = requestQueue.getStats();
    
    res.json({
        ...stats,
        requestQueue: requestQueueStats,
        timestamp: new Date().toISOString(),
        uptime: Date.now() - (wsPool.connectionHealth.values().next().value?.createdAt || Date.now())
    });
});

// 🌡️ Service Health Validation Endpoint
app.get('/api/service-health', async (req, res) => {
    try {
        const stats = wsPool.getPoolStats();
        const requestQueueStats = requestQueue.getStats();
        
        // Test basic connectivity
        const connectivityTests = {
            connectionPool: {
                totalConnections: stats.totalConnections,
                healthyConnections: stats.aggregateHealth.healthyConnections,
                status: stats.totalConnections > 0 ? 'AVAILABLE' : 'NO_CONNECTIONS'
            },
            requestQueue: {
                queueLength: requestQueueStats.queueLength,
                activeRequests: requestQueueStats.activeRequests,
                backoff: requestQueueStats.currentBackoff,
                status: requestQueueStats.queueLength < 50 ? 'HEALTHY' : 'OVERLOADED'
            },
            circuitBreaker: {
                state: stats.circuitBreaker.state,
                failures: stats.circuitBreaker.failures,
                rateLimitFailures: stats.circuitBreaker.rateLimitFailures,
                status: stats.circuitBreaker.state === 'CLOSED' ? 'HEALTHY' : 'DEGRADED'
            },
            redis: {
                available: !!redisClient,
                publisher: !!redisPublisher,
                status: (redisClient && redisPublisher) ? 'CONNECTED' : 'DISCONNECTED'
            }
        };
        
        // Overall service health
        const overallHealth = 
            connectivityTests.connectionPool.status === 'AVAILABLE' &&
            connectivityTests.requestQueue.status === 'HEALTHY' &&
            connectivityTests.circuitBreaker.status === 'HEALTHY'
                ? 'HEALTHY' : 'DEGRADED';
        
        const httpStatus = overallHealth === 'HEALTHY' ? 200 : 503;
        
        res.status(httpStatus).json({
            service: 'tradingview-websocket-api',
            health: overallHealth,
            timestamp: new Date().toISOString(),
            tests: connectivityTests,
            endpoints: {
                '/api/historical/:symbol/:timeframe': 'Main historical data endpoint',
                '/api/historical': 'Legacy historical data endpoint',
                '/api/quote': 'Real-time quote endpoint',
                '/api/symbols': 'Available symbols endpoint',
                '/health': 'System health endpoint',
                '/api/data-health': 'Data quality health check'
            },
            recommendations: overallHealth !== 'HEALTHY' ? [
                connectivityTests.connectionPool.status !== 'AVAILABLE' ? 'Restart TradingView connection pool' : null,
                connectivityTests.requestQueue.status !== 'HEALTHY' ? 'Clear request queue backlog' : null,
                connectivityTests.circuitBreaker.status !== 'HEALTHY' ? 'Wait for circuit breaker recovery' : null
            ].filter(Boolean) : []
        });
        
    } catch (error) {
        logger.error(`❌ Service health check failed: ${error.message}`);
        res.status(500).json({
            service: 'tradingview-websocket-api',
            health: 'ERROR',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 📊 Data Health Monitoring Endpoint
app.get('/api/data-health', async (req, res) => {
    try {
        const { symbol = 'EURUSD', timeframe = '1H' } = req.query;
        
        logger.info(`🔍 Data health check for ${symbol}/${timeframe}`);
        
        const healthCheck = await requestQueue.enqueue('health_check', `${symbol}_${timeframe}`, async () => {
            const connectionId = await wsPool.getOptimalConnection(symbol);
            const client = wsPool.connections.get(connectionId);
            
            if (!client) {
                throw new Error('No available pooled connection for health check');
            }
            
            const chart = new client.Session.Chart();
            
            // Set up chart session for health check
            chart.setMarket(symbol, {
                timeframe: timeframe === '1H' ? '60' : timeframe,
                range: 10 // Small sample for health check
            });

            return new Promise((resolve, reject) => {
                let timeout = setTimeout(() => {
                    reject(new Error('Health check timeout'));
                }, 15000);

                chart.onUpdate(() => {
                    if (chart.periods && chart.periods.length > 0) {
                        clearTimeout(timeout);
                        
                        const periods = chart.periods.slice(-5); // Last 5 periods
                        let validCandles = 0;
                        let flatCandles = 0;
                        let invalidCandles = 0;
                        
                        const analysis = periods.map(period => {
                            const ohlc = {
                                open: period.open,
                                high: period.high || period.max,
                                low: period.low || period.min,
                                close: period.close
                            };
                            
                            // Check if flat (all values identical)
                            const isFlat = ohlc.open === ohlc.high && ohlc.high === ohlc.low && ohlc.low === ohlc.close;
                            
                            // Check if invalid
                            const isInvalid = !ohlc.open || !ohlc.high || !ohlc.low || !ohlc.close ||
                                            ohlc.high < Math.max(ohlc.open, ohlc.close) ||
                                            ohlc.low > Math.min(ohlc.open, ohlc.close);
                            
                            if (isFlat) {
                                flatCandles++;
                            } else if (isInvalid) {
                                invalidCandles++;
                            } else {
                                validCandles++;
                            }
                            
                            const spread = ohlc.high - ohlc.low;
                            const avgPrice = (ohlc.high + ohlc.low) / 2;
                            const spreadPercentage = avgPrice > 0 ? (spread / avgPrice) * 100 : 0;
                            
                            return {
                                time: new Date(period.time * 1000).toISOString(),
                                ohlc,
                                spread,
                                spreadPercentage,
                                isFlat,
                                isInvalid,
                                isValid: !isFlat && !isInvalid
                            };
                        });
                        
                        // Clean up
                        try {
                            chart.delete();
                        } catch (cleanupError) {
                            logger.warn(`Health check cleanup error: ${cleanupError.message}`);
                        }
                        
                        resolve({
                            symbol,
                            timeframe,
                            totalPeriods: periods.length,
                            validCandles,
                            flatCandles,
                            invalidCandles,
                            healthScore: periods.length > 0 ? Math.round((validCandles / periods.length) * 100) : 0,
                            analysis
                        });
                    }
                });

                chart.onError((...err) => {
                    clearTimeout(timeout);
                    reject(new Error(`Health check chart error: ${err.join(' ')}`));
                });
            });
        });
        
        const overallHealth = healthCheck.healthScore >= 80 ? 'HEALTHY' : 
                             healthCheck.healthScore >= 50 ? 'WARNING' : 'UNHEALTHY';
        
        res.json({
            success: true,
            health: overallHealth,
            data: healthCheck,
            systemStats: {
                queueLength: requestQueue.getStats().queueLength,
                activeRequests: requestQueue.getStats().activeRequests,
                circuitBreakerState: wsPool.getPoolStats().circuitBreaker.state
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error(`❌ Data health check failed: ${error.message}`);
        res.status(500).json({
            success: false,
            health: 'ERROR',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Historical data endpoint (unchanged)
app.get('/api/historical', async (req, res) => {
    try {
        const { symbol, timeframe = '1D', range = 100, start_time, end_time } = req.query;
        
        if (!symbol) {
            return res.status(400).json({ error: 'Symbol parameter is required' });
        }

        logger.info(`Fetching historical data for ${symbol} (${timeframe})`);

        // Use request queue to prevent 429 errors
        const data = await requestQueue.enqueue('historical_legacy', `${symbol}_${timeframe}_${range}`, async () => {
            // Get pooled connection instead of creating new client
            const connectionId = await wsPool.getOptimalConnection(symbol);
            const client = wsPool.connections.get(connectionId);
            
            if (!client) {
                throw new Error('No available pooled connection for historical request');
            }
            
            const chart = new client.Session.Chart();

        chart.onError((...err) => {
            logger.error(`Chart error for ${symbol}:`, err);
        });

        const options = {
            timeframe: timeframe,
            range: parseInt(range)
        };

        if (start_time) options.to = parseInt(start_time);
        if (end_time) options.range = [parseInt(start_time), parseInt(end_time)];

        chart.setMarket(symbol, options);

        try {
            const data = await new Promise((resolve, reject) => {
                let timeout = setTimeout(() => {
                    reject(new Error('Timeout waiting for TradingView data'));
                }, 30000);

                chart.onUpdate(() => {
                    if (chart.periods && chart.periods.length > 0) {
                        clearTimeout(timeout);
                        
                        logger.info(`📊 Legacy endpoint processing ${chart.periods.length} TradingView periods for ${symbol}`);
                        
                        const formattedData = chart.periods
                            .map(period => {
                                // Validate OHLC data
                                const ohlc = {
                                    time: period.time,
                                    open: period.open,
                                    high: period.max || period.high,
                                    low: period.min || period.low,
                                    close: period.close,
                                    volume: period.volume || 0
                                };
                                
                                // Only include candles with valid spread
                                if (ohlc.high > ohlc.low && ohlc.high > 0 && ohlc.low > 0 && ohlc.open > 0 && ohlc.close > 0) {
                                    return ohlc;
                                }
                                
                                logger.warn(`⚠️ Legacy endpoint: Invalid OHLC data`, ohlc);
                                return null;
                            })
                            .filter(period => period !== null)
                            .reverse();

                        logger.info(`✅ Legacy endpoint: ${formattedData.length} valid candles processed`);
                        resolve(formattedData);
                    } else {
                        clearTimeout(timeout);
                        reject(new Error('No periods data received from TradingView'));
                    }
                });

                chart.onSymbolLoaded(() => {
                    logger.info(`✅ Market ${symbol} loaded successfully in legacy endpoint`);
                });
                
                chart.onError((...err) => {
                    clearTimeout(timeout);
                    const errorMsg = err.join(' ');
                    logger.error(`❌ Legacy endpoint chart error for ${symbol}: ${errorMsg}`);
                    reject(new Error(`TradingView chart error: ${errorMsg}`));
                });
            });

            return data;
        } finally {
            // ✅ FIXED: Clean up chart session within proper scope
            try {
                chart.delete();
                logger.debug(`🧹 Legacy endpoint chart session cleaned up for ${symbol}`);
            } catch (cleanupError) {
                logger.warn(`Chart cleanup error: ${cleanupError.message}`);
            }
        }
            
            if (!data || data.length === 0) {
                throw new Error('No valid historical data received from TradingView');
            }
            
            return data;
        });

        // 📊 Log data quality metrics
        const flatCount = data.filter(item => 
            item.open === item.high && item.high === item.low && item.low === item.close
        ).length;
        
        if (flatCount > 0) {
            logger.warn(`⚠️ Legacy endpoint detected ${flatCount}/${data.length} flat candles for ${symbol}`);
        }
        
        res.json({
            symbol,
            timeframe,
            data,
            count: data.length,
            quality: {
                total: data.length,
                flatCandles: flatCount,
                validCandles: data.length - flatCount,
                healthScore: data.length > 0 ? Math.round(((data.length - flatCount) / data.length) * 100) : 0
            }
        });

    } catch (error) {
        logger.error(`❌ Legacy historical endpoint error: ${error.message}`);
        
        // Determine appropriate error status based on error type
        let statusCode = 500;
        let errorMessage = 'Failed to fetch historical data';
        
        if (error.message.includes('429') || error.message.includes('rate limit')) {
            statusCode = 429;
            errorMessage = 'Rate limited by TradingView API';
        } else if (error.message.includes('timeout') || error.message.includes('Timeout')) {
            statusCode = 408;
            errorMessage = 'Request timeout - TradingView API slow to respond';
        } else if (error.message.includes('No available pooled connection')) {
            statusCode = 503;
            errorMessage = 'TradingView service unavailable';
        }
        
        res.status(statusCode).json({ 
            success: false,
            error: errorMessage,
            details: error.message,
            endpoint: 'legacy_historical',
            timestamp: new Date().toISOString()
        });
    }
});

// Single quote endpoint (uses cached data when available)
app.get('/api/quote', async (req, res) => {
    try {
        const { symbol } = req.query;
        
        if (!symbol) {
            return res.status(400).json({ error: 'Symbol parameter is required' });
        }

        // Try to get cached data first (if Redis is available)
        if (redisClient) {
            try {
                const cachedData = await redisClient.get(`websocket:market_data:${symbol}`);
                if (cachedData) {
                    const data = JSON.parse(cachedData);
                    logger.debug(`🎯 Cache HIT for quote: ${symbol}`);
                    return res.json({
                        symbol,
                        data: data.data,
                        timestamp: data.timestamp,
                        source: 'cached_websocket'
                    });
                } else {
                    logger.debug(`📝 Cache MISS for quote: ${symbol}`);
                }
            } catch (cacheError) {
                logger.debug(`Cache error for ${symbol}: ${cacheError.message} - proceeding without cache`);
                if (cacheError.message.includes('NOAUTH')) {
                    redisClient = null; // Disable Redis on auth failure
                }
            }
        }

        // Use request queue and connection pool to prevent 429 errors
        logger.info(`Fetching fresh quote data for ${symbol}`);

        const data = await requestQueue.enqueue('quote', symbol, async () => {
            // Get existing connection from pool instead of creating new client
            const connectionId = await wsPool.getOptimalConnection(symbol);
            const client = wsPool.connections.get(connectionId);
            
            if (!client) {
                throw new Error('No available pooled connection for quote request');
            }
            
            const quoteSession = new client.Session.Quote({ fields: 'all' });
            const market = new quoteSession.Market(symbol);

            try {
                const quoteData = await new Promise((resolve, reject) => {
                    let timeout = setTimeout(() => {
                        reject(new Error('Timeout waiting for quote data'));
                    }, 8000); // Reduced timeout for faster failure

                    market.onData((data) => {
                        clearTimeout(timeout);
                        resolve(data);
                    });

                    market.onError((...err) => {
                        clearTimeout(timeout);
                        const errorMsg = err.join(' ');
                        if (errorMsg.includes('429')) {
                            reject(new Error('TradingView rate limit exceeded - 429'));
                        } else {
                            reject(new Error(`Market error: ${errorMsg}`));
                        }
                    });
                });
                
                return quoteData;
                
            } finally {
                // Clean up session but don't end the pooled client
                try {
                    market.close();
                    quoteSession.delete();
                } catch (cleanupError) {
                    logger.warn(`Quote cleanup error: ${cleanupError.message}`);
                }
            }
        });

        res.json({
            symbol,
            data,
            timestamp: new Date().toISOString(),
            source: 'direct_websocket'
        });

    } catch (error) {
        logger.error(`Error fetching quote data: ${error.message}`);
        res.status(500).json({ error: error.message });
    }
});

// Market search endpoint (unchanged)
app.get('/api/search', async (req, res) => {
    try {
        const { query } = req.query;
        
        if (!query) {
            return res.status(400).json({ error: 'Query parameter is required' });
        }

        logger.info(`Searching markets for: ${query}`);

        const results = await TradingView.searchMarketV3(query);
        
        res.json({
            query,
            results: results.slice(0, 50),
            count: results.length
        });

    } catch (error) {
        logger.error(`Error searching markets: ${error.message}`);
        res.status(500).json({ error: error.message });
    }
});

// 📈 Historical data endpoint for chart initialization
app.get('/api/historical/:symbol/:timeframe', async (req, res) => {
    try {
        const { symbol, timeframe } = req.params;
        const limit = parseInt(req.query.limit) || 100;

        logger.info(`📊 Fetching historical data for ${symbol} - ${timeframe} (limit: ${limit})`);

        // Check Redis cache first (if available)
        let cacheKey = null;
        let cachedData = null;
        
        if (redisClient) {
            cacheKey = `historical:${symbol}:${timeframe}:${limit}`;
            try {
                cachedData = await redisClient.get(cacheKey);
                if (cachedData) {
                    logger.info(`🎯 Cache HIT for historical data: ${symbol}/${timeframe}`);
                    return res.json({
                        success: true,
                        symbol: symbol,
                        timeframe: timeframe,
                        data: JSON.parse(cachedData),
                        cached: true,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    logger.debug(`📝 Cache MISS for historical data: ${symbol}/${timeframe}`);
                }
            } catch (cacheError) {
                logger.warn(`Cache read error: ${cacheError.message} - proceeding without cache`);
                if (cacheError.message.includes('NOAUTH')) {
                    redisClient = null; // Disable Redis on auth failure
                }
            }
        } else {
            logger.debug(`📝 Redis unavailable - fetching fresh historical data for ${symbol}/${timeframe}`);
        }

        // Get available connection for historical data with health check
        const connectionId = await wsPool.getOptimalConnection(symbol);
        const client = wsPool.connections.get(connectionId);
        
        if (!client) {
            throw new Error('No TradingView connections available');
        }

        // Check connection health
        const health = wsPool.connectionHealth.get(connectionId);
        if (health && health.errorCount > 5) {
            logger.warn(`⚠️ Using connection ${connectionId} with ${health.errorCount} errors for ${symbol}`);
        }

        logger.debug(`📊 Using connection ${connectionId} for historical data: ${symbol}/${timeframe}`);

        // Use request queue for historical data to prevent 429 errors
        const chartData = await requestQueue.enqueue('historical', `${symbol}_${timeframe}_${limit}`, async () => {
            // Generate timeframe mapping
            const timeframeMap = {
                '1m': '1',
                '5m': '5', 
                '15m': '15',
                '1H': '60',
                '4H': '240',
                '1D': '1D',
                '1W': '1W',
                '1M': '1M'
            };

            const tvTimeframe = timeframeMap[timeframe] || timeframe;
            
            // Create Chart session for historical data using pooled connection
            const chart = new client.Session.Chart();
            
            // Set up chart session with symbol and timeframe
            chart.setMarket(symbol, {
                timeframe: tvTimeframe,
                range: limit
            });

            // Wait for chart data to be loaded with enhanced error handling
            try {
                return await new Promise((resolve, reject) => {
                let timeout = setTimeout(() => {
                    logger.error(`⏰ Timeout waiting for historical data: ${symbol}/${timeframe}`);
                    reject(new Error(`Timeout waiting for historical data for ${symbol}/${timeframe} after 30 seconds`));
                }, 30000);

                let hasReceivedData = false;
                let symbolLoaded = false;

                chart.onUpdate(() => {
                    if (chart.periods && chart.periods.length > 0) {
                        hasReceivedData = true;
                        clearTimeout(timeout);
                        logger.info(`✅ Chart data loaded for ${symbol}: ${chart.periods.length} periods`);
                        resolve(chart);
                    }
                });

                chart.onSymbolLoaded(() => {
                    symbolLoaded = true;
                    logger.info(`🔗 Symbol loaded for historical data: ${symbol}`);
                    
                    // If symbol loaded but no data after 5 seconds, still wait but log warning
                    setTimeout(() => {
                        if (!hasReceivedData) {
                            logger.warn(`⚠️ Symbol ${symbol} loaded but no historical data received yet - still waiting...`);
                        }
                    }, 5000);
                });

                chart.onError((...err) => {
                    clearTimeout(timeout);
                    const errorMessage = err.join(' ');
                    logger.error(`❌ Chart error for ${symbol}/${timeframe}:`, errorMessage);
                    
                    // Provide more specific error messages
                    if (errorMessage.includes('429')) {
                        reject(new Error('TradingView rate limit exceeded - 429'));
                    } else if (errorMessage.includes('ERR_SYMBOL_NOT_FOUND')) {
                        reject(new Error(`Symbol ${symbol} not found on TradingView. Please check the symbol format.`));
                    } else if (errorMessage.includes('ERR_SYMBOL_ERROR')) {
                        reject(new Error(`Symbol ${symbol} error - may be delisted or unavailable for timeframe ${timeframe}`));
                    } else if (errorMessage.includes('NOAUTH')) {
                        reject(new Error(`TradingView authentication failed - Redis connection issue`));
                    } else {
                        reject(new Error(`Chart error for ${symbol}/${timeframe}: ${errorMessage}`));
                    }
                });

                // Additional timeout for symbol loading
                setTimeout(() => {
                    if (!symbolLoaded && !hasReceivedData) {
                        logger.warn(`⚠️ No symbol loaded event received for ${symbol} after 10 seconds`);
                    }
                }, 10000);
                });
            } finally {
                // ✅ FIXED: Clean up chart session within proper scope
                try {
                    chart.delete();
                    logger.debug(`🧹 Main endpoint chart session cleaned up for ${symbol}/${timeframe}`);
                } catch (cleanupError) {
                    logger.warn(`Chart cleanup error: ${cleanupError.message}`);
                }
            }
        });

        // 🔍 COMPREHENSIVE TRADINGVIEW DATA ANALYSIS
        logger.info(`📊 Raw TradingView response analysis for ${symbol}/${timeframe}:`);
        logger.info(`   Chart object exists: ${!!chartData}`);
        logger.info(`   Chart periods exists: ${!!(chartData && chartData.periods)}`);
        logger.info(`   Chart periods length: ${chartData?.periods?.length || 0}`);
        
        if (chartData && chartData.periods && chartData.periods.length > 0) {
            const samplePeriod = chartData.periods[0];
            logger.info(`📋 Sample TradingView period structure:`, {
                keys: Object.keys(samplePeriod),
                time: samplePeriod.time,
                open: samplePeriod.open,
                high: samplePeriod.high,
                max: samplePeriod.max,
                low: samplePeriod.low, 
                min: samplePeriod.min,
                close: samplePeriod.close,
                volume: samplePeriod.volume
            });
        }
        
        // Convert TradingView data to standard OHLCV format
        const ohlcvData = [];
        
        if (chartData && chartData.periods && chartData.periods.length > 0) {
            logger.info(`✅ Processing ${chartData.periods.length} TradingView periods`);
            
            for (const period of chartData.periods.slice(-limit)) {
                // 🔍 DETAILED PERIOD ANALYSIS
                const rawOHLC = {
                    time: period.time,
                    open: period.open,
                    high: period.high || period.max,
                    low: period.low || period.min, 
                    close: period.close,
                    volume: period.volume
                };
                
                // Validate we have actual OHLC data (not all the same value)
                const hasValidSpread = (
                    rawOHLC.high !== rawOHLC.low && 
                    rawOHLC.high > 0 && 
                    rawOHLC.low > 0 &&
                    rawOHLC.open > 0 &&
                    rawOHLC.close > 0
                );
                
                if (!hasValidSpread) {
                    logger.warn(`⚠️ Invalid OHLC spread detected:`, rawOHLC);
                    continue; // Skip invalid data points
                }
                
                ohlcvData.push({
                    time: period.time * 1000, // Convert to milliseconds
                    open: period.open,
                    high: period.high || period.max, // TradingView uses 'max' for high
                    low: period.low || period.min,   // TradingView uses 'min' for low  
                    close: period.close,
                    volume: period.volume || 0
                });
            }
            
            logger.info(`✅ Successfully processed ${ohlcvData.length}/${chartData.periods.length} valid OHLCV candles`);
            
            if (ohlcvData.length > 0) {
                const first = ohlcvData[0];
                const last = ohlcvData[ohlcvData.length - 1];
                logger.info(`📊 OHLCV Data Range:`, {
                    first: { time: new Date(first.time), open: first.open, high: first.high, low: first.low, close: first.close },
                    last: { time: new Date(last.time), open: last.open, high: last.high, low: last.low, close: last.close }
                });
            }
        } else {
            logger.error(`❌ NO VALID TRADINGVIEW DATA RECEIVED for ${symbol}/${timeframe}`);
            logger.error(`   Chart exists: ${!!chartData}`);
            logger.error(`   Periods exist: ${!!(chartData && chartData.periods)}`);
            logger.error(`   Periods length: ${chartData?.periods?.length || 0}`);
            
            // Return empty array instead of mock data
            return res.status(500).json({
                success: false,
                error: 'No valid historical data available from TradingView',
                symbol: symbol,
                timeframe: timeframe,
                details: 'TradingView API returned no periods data',
                timestamp: new Date().toISOString()
            });
        }

        // Cache the result for 5 minutes (if Redis is available)
        if (redisClient && cacheKey) {
            try {
                await redisClient.setEx(cacheKey, 300, JSON.stringify(ohlcvData));
                logger.info(`💾 Cached historical data: ${symbol}/${timeframe}`);
            } catch (cacheError) {
                logger.warn(`Cache write error: ${cacheError.message}`);
                if (cacheError.message.includes('NOAUTH')) {
                    redisClient = null; // Disable Redis on auth failure
                }
            }
        }

        res.json({
            success: true,
            symbol: symbol,
            timeframe: timeframe,
            data: ohlcvData,
            cached: false,
            count: ohlcvData.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error(`❌ Error fetching historical data for ${req.params.symbol}: ${error.message}`);
        
        // Determine appropriate error status based on error type
        let statusCode = 500;
        let errorMessage = 'Failed to fetch historical data';
        
        if (error.message.includes('429') || error.message.includes('rate limit')) {
            statusCode = 429;
            errorMessage = 'Rate limited by TradingView API';
        } else if (error.message.includes('timeout') || error.message.includes('Timeout')) {
            statusCode = 408;
            errorMessage = 'Request timeout - TradingView API slow to respond';
        } else if (error.message.includes('No TradingView connections')) {
            statusCode = 503;
            errorMessage = 'TradingView service unavailable';
        } else if (error.message.includes('Circuit breaker')) {
            statusCode = 503;
            errorMessage = 'TradingView service temporarily unavailable';
        }
        
        res.status(statusCode).json({
            success: false,
            error: errorMessage,
            details: error.message,
            symbol: req.params.symbol,
            timeframe: req.params.timeframe,
            timestamp: new Date().toISOString(),
            retryAfter: statusCode === 429 ? 60 : undefined // Suggest retry after 60 seconds for rate limits
        });
    }
});

// 🚫 MOCK DATA FUNCTIONS REMOVED - ONLY REAL TRADINGVIEW DATA ALLOWED
// No fallback data generation - if TradingView fails, we fail gracefully with proper error messages

// 📊 Symbols endpoint for frontend symbol selector
app.get('/api/symbols', (req, res) => {
    try {
        logger.info('📊 Frontend requested symbols list');
        
        // 140+ symbols organized by category for trading interface
        const symbols = [
            // 💱 FOREX (28 pairs)
            { symbol: 'EURUSD', name: 'Euro / US Dollar', category: 'forex', description: 'EUR to USD currency pair' },
            { symbol: 'GBPUSD', name: 'British Pound / US Dollar', category: 'forex', description: 'GBP to USD currency pair' },
            { symbol: 'USDJPY', name: 'US Dollar / Japanese Yen', category: 'forex', description: 'USD to JPY currency pair' },
            { symbol: 'USDCHF', name: 'US Dollar / Swiss Franc', category: 'forex', description: 'USD to CHF currency pair' },
            { symbol: 'AUDUSD', name: 'Australian Dollar / US Dollar', category: 'forex', description: 'AUD to USD currency pair' },
            { symbol: 'USDCAD', name: 'US Dollar / Canadian Dollar', category: 'forex', description: 'USD to CAD currency pair' },
            { symbol: 'NZDUSD', name: 'New Zealand Dollar / US Dollar', category: 'forex', description: 'NZD to USD currency pair' },
            { symbol: 'EURGBP', name: 'Euro / British Pound', category: 'forex', description: 'EUR to GBP currency pair' },
            { symbol: 'EURJPY', name: 'Euro / Japanese Yen', category: 'forex', description: 'EUR to JPY currency pair' },
            { symbol: 'EURCHF', name: 'Euro / Swiss Franc', category: 'forex', description: 'EUR to CHF currency pair' },
            { symbol: 'EURAUD', name: 'Euro / Australian Dollar', category: 'forex', description: 'EUR to AUD currency pair' },
            { symbol: 'EURCAD', name: 'Euro / Canadian Dollar', category: 'forex', description: 'EUR to CAD currency pair' },
            { symbol: 'GBPJPY', name: 'British Pound / Japanese Yen', category: 'forex', description: 'GBP to JPY currency pair' },
            { symbol: 'GBPCHF', name: 'British Pound / Swiss Franc', category: 'forex', description: 'GBP to CHF currency pair' },
            { symbol: 'GBPAUD', name: 'British Pound / Australian Dollar', category: 'forex', description: 'GBP to AUD currency pair' },
            { symbol: 'GBPCAD', name: 'British Pound / Canadian Dollar', category: 'forex', description: 'GBP to CAD currency pair' },
            { symbol: 'CHFJPY', name: 'Swiss Franc / Japanese Yen', category: 'forex', description: 'CHF to JPY currency pair' },
            { symbol: 'AUDCHF', name: 'Australian Dollar / Swiss Franc', category: 'forex', description: 'AUD to CHF currency pair' },
            { symbol: 'AUDJPY', name: 'Australian Dollar / Japanese Yen', category: 'forex', description: 'AUD to JPY currency pair' },
            { symbol: 'AUDCAD', name: 'Australian Dollar / Canadian Dollar', category: 'forex', description: 'AUD to CAD currency pair' },
            { symbol: 'CADCHF', name: 'Canadian Dollar / Swiss Franc', category: 'forex', description: 'CAD to CHF currency pair' },
            { symbol: 'CADJPY', name: 'Canadian Dollar / Japanese Yen', category: 'forex', description: 'CAD to JPY currency pair' },
            { symbol: 'NZDCHF', name: 'New Zealand Dollar / Swiss Franc', category: 'forex', description: 'NZD to CHF currency pair' },
            { symbol: 'NZDJPY', name: 'New Zealand Dollar / Japanese Yen', category: 'forex', description: 'NZD to JPY currency pair' },
            { symbol: 'NZDCAD', name: 'New Zealand Dollar / Canadian Dollar', category: 'forex', description: 'NZD to CAD currency pair' },
            { symbol: 'AUDNZD', name: 'Australian Dollar / New Zealand Dollar', category: 'forex', description: 'AUD to NZD currency pair' },
            { symbol: 'GBPNZD', name: 'British Pound / New Zealand Dollar', category: 'forex', description: 'GBP to NZD currency pair' },
            { symbol: 'EURNZD', name: 'Euro / New Zealand Dollar', category: 'forex', description: 'EUR to NZD currency pair' },
            
            // 🪙 CRYPTOCURRENCY (25 coins)
            { symbol: 'BTCUSD', name: 'Bitcoin / US Dollar', category: 'crypto', description: 'Bitcoin cryptocurrency' },
            { symbol: 'ETHUSD', name: 'Ethereum / US Dollar', category: 'crypto', description: 'Ethereum cryptocurrency' },
            { symbol: 'ADAUSD', name: 'Cardano / US Dollar', category: 'crypto', description: 'Cardano cryptocurrency' },
            { symbol: 'XRPUSD', name: 'Ripple / US Dollar', category: 'crypto', description: 'Ripple cryptocurrency' },
            { symbol: 'SOLUSD', name: 'Solana / US Dollar', category: 'crypto', description: 'Solana cryptocurrency' },
            { symbol: 'DOTUSD', name: 'Polkadot / US Dollar', category: 'crypto', description: 'Polkadot cryptocurrency' },
            { symbol: 'AVAXUSD', name: 'Avalanche / US Dollar', category: 'crypto', description: 'Avalanche cryptocurrency' },
            { symbol: 'MATICUSD', name: 'Polygon / US Dollar', category: 'crypto', description: 'Polygon cryptocurrency' },
            { symbol: 'LTCUSD', name: 'Litecoin / US Dollar', category: 'crypto', description: 'Litecoin cryptocurrency' },
            { symbol: 'LINKUSD', name: 'Chainlink / US Dollar', category: 'crypto', description: 'Chainlink cryptocurrency' },
            { symbol: 'UNIUSD', name: 'Uniswap / US Dollar', category: 'crypto', description: 'Uniswap cryptocurrency' },
            { symbol: 'ALGOUSD', name: 'Algorand / US Dollar', category: 'crypto', description: 'Algorand cryptocurrency' },
            { symbol: 'ATOMUSD', name: 'Cosmos / US Dollar', category: 'crypto', description: 'Cosmos cryptocurrency' },
            { symbol: 'FILUSD', name: 'Filecoin / US Dollar', category: 'crypto', description: 'Filecoin cryptocurrency' },
            { symbol: 'VETUSD', name: 'VeChain / US Dollar', category: 'crypto', description: 'VeChain cryptocurrency' },
            { symbol: 'ICPUSD', name: 'Internet Computer / US Dollar', category: 'crypto', description: 'Internet Computer cryptocurrency' },
            { symbol: 'DASHUSD', name: 'Dash / US Dollar', category: 'crypto', description: 'Dash cryptocurrency' },
            { symbol: 'BCHUSD', name: 'Bitcoin Cash / US Dollar', category: 'crypto', description: 'Bitcoin Cash cryptocurrency' },
            { symbol: 'XLMUSD', name: 'Stellar / US Dollar', category: 'crypto', description: 'Stellar cryptocurrency' },
            { symbol: 'TRXUSD', name: 'TRON / US Dollar', category: 'crypto', description: 'TRON cryptocurrency' },
            { symbol: 'EOSUSD', name: 'EOS / US Dollar', category: 'crypto', description: 'EOS cryptocurrency' },
            { symbol: 'XMRUSD', name: 'Monero / US Dollar', category: 'crypto', description: 'Monero cryptocurrency' },
            { symbol: 'ZECUSD', name: 'Zcash / US Dollar', category: 'crypto', description: 'Zcash cryptocurrency' },
            { symbol: 'NEOUSD', name: 'Neo / US Dollar', category: 'crypto', description: 'Neo cryptocurrency' },
            { symbol: 'MKRUSD', name: 'Maker / US Dollar', category: 'crypto', description: 'Maker cryptocurrency' },
            
            // 📈 US STOCKS (30 stocks)
            { symbol: 'AAPL', name: 'Apple Inc.', category: 'stocks', description: 'Apple Inc. technology stock' },
            { symbol: 'TSLA', name: 'Tesla Inc.', category: 'stocks', description: 'Tesla Inc. electric vehicle stock' },
            { symbol: 'GOOGL', name: 'Alphabet Inc.', category: 'stocks', description: 'Alphabet Inc. Class A stock' },
            { symbol: 'AMZN', name: 'Amazon.com Inc.', category: 'stocks', description: 'Amazon.com Inc. stock' },
            { symbol: 'MSFT', name: 'Microsoft Corporation', category: 'stocks', description: 'Microsoft Corporation stock' },
            { symbol: 'NVDA', name: 'NVIDIA Corporation', category: 'stocks', description: 'NVIDIA Corporation stock' },
            { symbol: 'META', name: 'Meta Platforms Inc.', category: 'stocks', description: 'Meta Platforms Inc. stock' },
            { symbol: 'NFLX', name: 'Netflix Inc.', category: 'stocks', description: 'Netflix Inc. streaming stock' },
            { symbol: 'AMD', name: 'Advanced Micro Devices', category: 'stocks', description: 'AMD semiconductor stock' },
            { symbol: 'INTC', name: 'Intel Corporation', category: 'stocks', description: 'Intel Corporation stock' },
            { symbol: 'CRM', name: 'Salesforce Inc.', category: 'stocks', description: 'Salesforce Inc. cloud software stock' },
            { symbol: 'ORCL', name: 'Oracle Corporation', category: 'stocks', description: 'Oracle Corporation database stock' },
            { symbol: 'UBER', name: 'Uber Technologies Inc.', category: 'stocks', description: 'Uber Technologies rideshare stock' },
            { symbol: 'LYFT', name: 'Lyft Inc.', category: 'stocks', description: 'Lyft Inc. rideshare stock' },
            { symbol: 'SHOP', name: 'Shopify Inc.', category: 'stocks', description: 'Shopify Inc. e-commerce stock' },
            { symbol: 'SQ', name: 'Block Inc.', category: 'stocks', description: 'Block Inc. (Square) fintech stock' },
            { symbol: 'PYPL', name: 'PayPal Holdings Inc.', category: 'stocks', description: 'PayPal Holdings payment stock' },
            { symbol: 'ZOOM', name: 'Zoom Video Communications', category: 'stocks', description: 'Zoom Video Communications stock' },
            { symbol: 'ROKU', name: 'Roku Inc.', category: 'stocks', description: 'Roku Inc. streaming platform stock' },
            { symbol: 'SPOT', name: 'Spotify Technology S.A.', category: 'stocks', description: 'Spotify music streaming stock' },
            { symbol: 'TWTR', name: 'Twitter Inc.', category: 'stocks', description: 'Twitter Inc. social media stock' },
            { symbol: 'SNAP', name: 'Snap Inc.', category: 'stocks', description: 'Snap Inc. social media stock' },
            { symbol: 'JNJ', name: 'Johnson & Johnson', category: 'stocks', description: 'Johnson & Johnson healthcare stock' },
            { symbol: 'PFE', name: 'Pfizer Inc.', category: 'stocks', description: 'Pfizer Inc. pharmaceutical stock' },
            { symbol: 'KO', name: 'The Coca-Cola Company', category: 'stocks', description: 'Coca-Cola beverage stock' },
            { symbol: 'DIS', name: 'The Walt Disney Company', category: 'stocks', description: 'Disney entertainment stock' },
            { symbol: 'NKE', name: 'NIKE Inc.', category: 'stocks', description: 'Nike Inc. sportswear stock' },
            { symbol: 'MCD', name: 'McDonald\'s Corporation', category: 'stocks', description: 'McDonald\'s restaurant stock' },
            { symbol: 'WMT', name: 'Walmart Inc.', category: 'stocks', description: 'Walmart Inc. retail stock' },
            { symbol: 'V', name: 'Visa Inc.', category: 'stocks', description: 'Visa Inc. financial services stock' },
            
            // ⚡ COMMODITIES (12 commodities)
            { symbol: 'GOLD', name: 'Gold Spot Price', category: 'commodities', description: 'Gold precious metal commodity' },
            { symbol: 'SILVER', name: 'Silver Spot Price', category: 'commodities', description: 'Silver precious metal commodity' },
            { symbol: 'CRUDE_OIL', name: 'Crude Oil WTI', category: 'commodities', description: 'West Texas Intermediate crude oil' },
            { symbol: 'BRENT_OIL', name: 'Brent Oil', category: 'commodities', description: 'Brent crude oil commodity' },
            { symbol: 'NATURAL_GAS', name: 'Natural Gas', category: 'commodities', description: 'Natural gas commodity' },
            { symbol: 'COPPER', name: 'Copper', category: 'commodities', description: 'Copper industrial metal' },
            { symbol: 'PLATINUM', name: 'Platinum', category: 'commodities', description: 'Platinum precious metal' },
            { symbol: 'PALLADIUM', name: 'Palladium', category: 'commodities', description: 'Palladium precious metal' },
            { symbol: 'WHEAT', name: 'Wheat', category: 'commodities', description: 'Wheat agricultural commodity' },
            { symbol: 'CORN', name: 'Corn', category: 'commodities', description: 'Corn agricultural commodity' },
            { symbol: 'SOYBEANS', name: 'Soybeans', category: 'commodities', description: 'Soybeans agricultural commodity' },
            { symbol: 'COFFEE', name: 'Coffee', category: 'commodities', description: 'Coffee agricultural commodity' },
            
            // 📊 INDICES (10 indices)
            { symbol: 'SPX500', name: 'S&P 500 Index', category: 'indices', description: 'S&P 500 US stock market index' },
            { symbol: 'US30', name: 'Dow Jones 30', category: 'indices', description: 'Dow Jones Industrial Average' },
            { symbol: 'NAS100', name: 'NASDAQ 100', category: 'indices', description: 'NASDAQ 100 technology index' },
            { symbol: 'UK100', name: 'FTSE 100', category: 'indices', description: 'FTSE 100 UK stock index' },
            { symbol: 'GER40', name: 'DAX 40', category: 'indices', description: 'DAX 40 German stock index' },
            { symbol: 'EUR50', name: 'EURO STOXX 50', category: 'indices', description: 'EURO STOXX 50 European index' },
            { symbol: 'JP225', name: 'Nikkei 225', category: 'indices', description: 'Nikkei 225 Japanese stock index' },
            { symbol: 'AUS200', name: 'ASX 200', category: 'indices', description: 'ASX 200 Australian stock index' },
            { symbol: 'HK50', name: 'Hang Seng 50', category: 'indices', description: 'Hang Seng Hong Kong index' },
            { symbol: 'VIX', name: 'Volatility Index', category: 'indices', description: 'CBOE Volatility Index (Fear Index)' },
            
            // 💼 ETFs (12 ETFs)
            { symbol: 'SPY', name: 'SPDR S&P 500 ETF', category: 'etfs', description: 'S&P 500 ETF trust fund' },
            { symbol: 'QQQ', name: 'Invesco QQQ Trust', category: 'etfs', description: 'NASDAQ 100 tracking ETF' },
            { symbol: 'GLD', name: 'SPDR Gold Shares', category: 'etfs', description: 'Gold commodity ETF' },
            { symbol: 'SLV', name: 'iShares Silver Trust', category: 'etfs', description: 'Silver commodity ETF' },
            { symbol: 'TLT', name: 'iShares 20+ Year Treasury Bond ETF', category: 'etfs', description: 'Long-term Treasury bond ETF' },
            { symbol: 'VTI', name: 'Vanguard Total Stock Market ETF', category: 'etfs', description: 'Total US stock market ETF' },
            { symbol: 'EEM', name: 'iShares MSCI Emerging Markets ETF', category: 'etfs', description: 'Emerging markets ETF' },
            { symbol: 'IWM', name: 'iShares Russell 2000 ETF', category: 'etfs', description: 'Small-cap US stocks ETF' },
            { symbol: 'XLF', name: 'Financial Select Sector SPDR Fund', category: 'etfs', description: 'Financial sector ETF' },
            { symbol: 'XLE', name: 'Energy Select Sector SPDR Fund', category: 'etfs', description: 'Energy sector ETF' },
            { symbol: 'XLK', name: 'Technology Select Sector SPDR Fund', category: 'etfs', description: 'Technology sector ETF' },
            { symbol: 'XLV', name: 'Health Care Select Sector SPDR Fund', category: 'etfs', description: 'Healthcare sector ETF' }
        ];
        
        // Return success response with categorized symbols
        res.json({
            success: true,
            symbols: symbols,
            total: symbols.length,
            categories: {
                forex: symbols.filter(s => s.category === 'forex').length,
                crypto: symbols.filter(s => s.category === 'crypto').length,
                stocks: symbols.filter(s => s.category === 'stocks').length,
                commodities: symbols.filter(s => s.category === 'commodities').length,
                indices: symbols.filter(s => s.category === 'indices').length,
                etfs: symbols.filter(s => s.category === 'etfs').length
            },
            timestamp: new Date().toISOString(),
            source: 'revolutionary_websocket_service'
        });
        
    } catch (error) {
        logger.error(`❌ Error serving symbols: ${error.message}`);
        res.status(500).json({ 
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 🚀 PURE WEBSOCKET CONNECTION HANDLING - NO POLLING!
io.on('connection', (socket) => {
    logger.info(`📡 Client connected: ${socket.id}`);
    clientSubscriptions.set(socket.id, new Set());

    socket.on('subscribe', async (data) => {
        try {
            const { symbol } = data;
            
            if (!symbol) {
                socket.emit('error', { message: 'Symbol is required' });
                return;
            }

            // 🛡️ RATE LIMITING AND DEDUPLICATION SAFEGUARDS
            const now = Date.now();
            let clientLimits = clientRateLimits.get(socket.id);
            
            if (!clientLimits) {
                clientLimits = {
                    lastSubscribe: 0,
                    subscribeCount: 0,
                    subscriptions: new Set(),
                    subscribeAttempts: []
                };
                clientRateLimits.set(socket.id, clientLimits);
            }
            
            // Check subscription cooldown to prevent rapid-fire subscriptions
            if (now - clientLimits.lastSubscribe < subscriptionCooldown) {
                logger.warn(`🚨 Rate limit hit: Client ${socket.id} subscribing too rapidly to ${symbol} (${now - clientLimits.lastSubscribe}ms since last)`);
                socket.emit('error', { 
                    message: `Rate limit: Please wait ${subscriptionCooldown}ms between subscriptions`,
                    code: 'RATE_LIMIT_SUBSCRIBE_COOLDOWN'
                });
                return;
            }
            
            // Check if already subscribed to prevent duplicate subscriptions
            if (clientLimits.subscriptions.has(symbol)) {
                logger.warn(`🚨 Duplicate subscription: Client ${socket.id} already subscribed to ${symbol}`);
                socket.emit('subscription_confirmed', { 
                    symbol, 
                    message: 'Already subscribed - using existing WebSocket subscription',
                    duplicate: true
                });
                return;
            }
            
            // Check maximum subscriptions per client
            if (clientLimits.subscriptions.size >= maxSubscriptionsPerClient) {
                logger.warn(`🚨 Subscription limit: Client ${socket.id} has reached maximum ${maxSubscriptionsPerClient} subscriptions`);
                socket.emit('error', { 
                    message: `Maximum ${maxSubscriptionsPerClient} subscriptions allowed per client`,
                    code: 'MAX_SUBSCRIPTIONS_REACHED'
                });
                return;
            }
            
            // Track subscription attempts per minute
            clientLimits.subscribeAttempts = clientLimits.subscribeAttempts.filter(time => now - time < 60000); // Keep last minute
            if (clientLimits.subscribeAttempts.length >= maxSubscriptionsPerMinute) {
                logger.warn(`🚨 Rate limit: Client ${socket.id} exceeded ${maxSubscriptionsPerMinute} subscription attempts per minute`);
                socket.emit('error', { 
                    message: `Rate limit: Maximum ${maxSubscriptionsPerMinute} subscription attempts per minute`,
                    code: 'RATE_LIMIT_PER_MINUTE'
                });
                return;
            }
            
            // Update rate limiting tracking
            clientLimits.lastSubscribe = now;
            clientLimits.subscribeCount++;
            clientLimits.subscribeAttempts.push(now);
            clientLimits.subscriptions.add(symbol);

            logger.info(`🔗 Client ${socket.id} subscribing to ${symbol} (${clientLimits.subscriptions.size}/${maxSubscriptionsPerClient})`);
            logger.debug(`📊 Client ${socket.id} rate limit status: ${clientLimits.subscribeAttempts.length}/${maxSubscriptionsPerMinute} attempts/min`);

            // Subscribe to symbol with pure WebSocket events
            await wsPool.subscribeToSymbol(symbol, socket.id, (marketData) => {
                // Emit real-time update to client
                socket.emit('market_data', {
                    symbol: marketData.symbol,
                    data: marketData.data,
                    timestamp: marketData.timestamp,
                    source: 'websocket_stream'
                });
            });

            // Track client subscription
            clientSubscriptions.get(socket.id).add(symbol);

            socket.emit('subscription_confirmed', { 
                symbol, 
                message: 'WebSocket subscription active - real-time updates enabled' 
            });

        } catch (error) {
            logger.error(`Error in subscribe: ${error.message}`);
            socket.emit('error', { message: error.message });
        }
    });

    socket.on('unsubscribe', (data) => {
        const { symbol } = data;
        
        if (symbol && clientSubscriptions.has(socket.id)) {
            wsPool.unsubscribeFromSymbol(symbol, socket.id);
            clientSubscriptions.get(socket.id).delete(symbol);
            
            // Update rate limiting state
            const clientLimits = clientRateLimits.get(socket.id);
            if (clientLimits) {
                clientLimits.subscriptions.delete(symbol);
            }
            
            logger.info(`🔌 Client ${socket.id} unsubscribed from ${symbol}`);
        }
    });

    socket.on('disconnect', () => {
        logger.info(`📡 Client disconnected: ${socket.id}`);
        
        // Clean up all subscriptions for this client
        const symbols = clientSubscriptions.get(socket.id) || new Set();
        for (const symbol of symbols) {
            wsPool.unsubscribeFromSymbol(symbol, socket.id);
        }
        clientSubscriptions.delete(socket.id);
        
        // Clean up rate limiting state
        clientRateLimits.delete(socket.id);
        
        logger.info(`🧹 Cleaned up ${symbols.size} subscriptions and rate limit state for ${socket.id}`);
    });
});

// Error handling
process.on('unhandledRejection', (reason, promise) => {
    logger.error('💥 Unhandled Promise Rejection:');
    logger.error(`   Promise: ${promise}`);
    logger.error(`   Reason: ${reason}`);
    if (reason && reason.stack) {
        logger.error(`   Stack: ${reason.stack}`);
    }
});

process.on('uncaughtException', (error) => {
    logger.error('💥 Uncaught Exception Details:');
    logger.error(`   Message: ${error.message}`);
    logger.error(`   Name: ${error.name}`);
    logger.error(`   Stack: ${error.stack}`);
    logger.error('   Full Error Object:', error);
    logger.error('🛑 Service will now exit due to uncaught exception');
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    logger.info('🛑 Shutting down WebSocket service...');
    
    // Close Express backend connection
    if (expressBackendClient && expressBackendClient.connected) {
        expressBackendClient.emit('revolutionary_service_disconnecting', {
            reason: 'graceful_shutdown',
            timestamp: new Date().toISOString()
        });
        expressBackendClient.disconnect();
        logger.info('🔌 Express backend connection closed');
    }
    
    // Close Redis connections
    if (redisClient) await redisClient.quit();
    if (redisPublisher) await redisPublisher.quit();
    
    server.close(() => {
        logger.info('✅ WebSocket service shutdown complete');
        process.exit(0);
    });
});

// Initialize and start server
async function startServer() {
    try {
        logger.info('🚀 Initializing REVOLUTIONARY WebSocket-Only Service...');
        
        // Initialize WebSocket connection pool
        await wsPool.initialize();
        
        // Initialize Express backend connection
        await initializeExpressBackendConnection();
        
        // Optimized challenge engine symbol subscriptions with staggered startup
        if (ENABLE_BACKEND_INTEGRATION && ENABLE_CHALLENGE_ENGINE_FEED) {
            logger.info(`📊 Subscribing to ${CHALLENGE_ENGINE_SYMBOLS.length} challenge engine symbols with staggered startup...`);
            
            // Stagger subscriptions to prevent overwhelming TradingView API
            const subscribeWithDelay = async (symbols, delay = 500) => {
                let subscribed = 0;
                let failed = 0;
                
                for (let i = 0; i < symbols.length; i++) {
                    const symbol = symbols[i];
                    
                    try {
                        // Wait between subscriptions to respect rate limits
                        if (i > 0) {
                            await new Promise(resolve => setTimeout(resolve, delay));
                        }
                        
                        await wsPool.subscribeToSymbol(symbol, 'challenge_engine_feed', (marketData) => {
                            // Data automatically forwarded to Express backend via publishMarketData
                        });
                        
                        subscribed++;
                        logger.debug(`✅ Subscribed to ${symbol} for challenge engine (${subscribed}/${symbols.length})`);
                        
                        // Log progress every 10 symbols
                        if (subscribed % 10 === 0) {
                            logger.info(`📊 Challenge engine progress: ${subscribed}/${symbols.length} symbols subscribed`);
                        }
                        
                    } catch (error) {
                        failed++;
                        logger.warn(`⚠️ Failed to subscribe to ${symbol}: ${error.message}`);
                        
                        // If we get rate limit errors, increase delay
                        if (error.message.includes('429') || error.message.includes('rate limit')) {
                            delay = Math.min(delay * 1.5, 2000); // Increase delay up to 2 seconds
                            logger.warn(`⏰ Increased subscription delay to ${delay}ms due to rate limiting`);
                        }
                    }
                }
                
                logger.info(`✅ Challenge engine startup complete: ${subscribed} subscribed, ${failed} failed`);
            };
            
            // Start subscriptions in background
            subscribeWithDelay(CHALLENGE_ENGINE_SYMBOLS, 500).catch(error => {
                logger.error(`❌ Challenge engine subscription error: ${error.message}`);
            });
        } else {
            logger.info('📴 Challenge engine feed disabled - no automatic symbol subscriptions');
        }
        
        server.listen(PORT, (err) => {
            if (err) {
                logger.error(`💥 Failed to start server on port ${PORT}: ${err.message}`);
                process.exit(1);
                return;
            }
            
            logger.info(`🌟 REVOLUTIONARY WebSocket TradingView service running on port ${PORT}`);
            logger.info('🎯 Features: ZERO polling, 100% push-based, multi-connection architecture');
            logger.info('⚡ Ready for 138+ symbols with sub-second updates!');
            logger.info('🏗️ HYBRID ARCHITECTURE ACTIVE:');
            logger.info(`   • Direct Frontend Access: ${ENABLE_DIRECT_FRONTEND_ACCESS ? 'ENABLED' : 'DISABLED'}`);
            logger.info(`   • Express Backend Integration: ${ENABLE_BACKEND_INTEGRATION ? 'ENABLED' : 'DISABLED'}`);
            logger.info(`   • Challenge Engine Feed: ${ENABLE_CHALLENGE_ENGINE_FEED ? 'ENABLED' : 'DISABLED'}`);
            if (ENABLE_CHALLENGE_ENGINE_FEED) {
                logger.info(`   • Challenge Engine Symbols: ${CHALLENGE_ENGINE_SYMBOLS.length} symbols`);
            }
        });
        
        server.on('error', (error) => {
            if (error.code === 'EADDRINUSE') {
                logger.error(`💥 Port ${PORT} is already in use. Please stop any running services on this port or use a different port.`);
            } else {
                logger.error(`💥 Server error: ${error.message}`);
            }
            process.exit(1);
        });
        
    } catch (error) {
        logger.error(`💥 Failed to start WebSocket service: ${error.message}`);
        process.exit(1);
    }
}

startServer();

module.exports = { app, server, wsPool };