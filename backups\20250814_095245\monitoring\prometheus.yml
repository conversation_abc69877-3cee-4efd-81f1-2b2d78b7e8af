# Prometheus Configuration for TradingView API Enterprise
# Optimized for financial data monitoring with comprehensive metrics collection

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'tradingview-api'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configuration
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # FastAPI instances monitoring
  - job_name: 'fastapi-servers'
    static_configs:
      - targets: 
        - 'api-server-1:8000'
        - 'api-server-2:8000'
        - 'api-server-3:8000'
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Specialized collectors monitoring
  - job_name: 'forex-collector'
    static_configs:
      - targets: ['forex-collector:3001']
    scrape_interval: 15s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: collector_type
        replacement: 'forex'

  - job_name: 'crypto-collector'
    static_configs:
      - targets: ['crypto-collector:3001']
    scrape_interval: 10s  # More frequent for crypto volatility
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: collector_type
        replacement: 'crypto'

  - job_name: 'indices-collector'
    static_configs:
      - targets: ['indices-collector:3001']
    scrape_interval: 15s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: collector_type
        replacement: 'indices'

  - job_name: 'commodities-collector'
    static_configs:
      - targets: ['commodities-collector:3001']
    scrape_interval: 20s  # Less frequent for commodities
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: collector_type
        replacement: 'commodities'

  # Webhook service monitoring
  - job_name: 'webhook-service'
    static_configs:
      - targets: ['webhook-service:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Redis monitoring
  - job_name: 'redis-master'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: redis_instance
        replacement: 'master'

  # NGINX load balancer monitoring
  - job_name: 'nginx-lb'
    static_configs:
      - targets: ['nginx-lb:8080']
    scrape_interval: 30s
    metrics_path: /nginx_status
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: 'nginx-lb'

  # Node exporter for system metrics (if deployed)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor for container metrics (if deployed)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Custom business metrics from API endpoints
  - job_name: 'business-metrics'
    static_configs:
      - targets: 
        - 'api-server-1:8000'
        - 'api-server-2:8000'
        - 'api-server-3:8000'
    scrape_interval: 30s
    metrics_path: /api/system/metrics
    params:
      format: ['prometheus']
    basic_auth:
      username: 'prometheus'
      password: 'monitoring-secret'

  # Health check endpoints for uptime monitoring
  - job_name: 'health-checks'
    static_configs:
      - targets:
        - 'api-server-1:8000'
        - 'api-server-2:8000'
        - 'api-server-3:8000'
        - 'webhook-service:8000'
        - 'forex-collector:3001'
        - 'crypto-collector:3001'
        - 'indices-collector:3001'
        - 'commodities-collector:3001'
    scrape_interval: 10s
    metrics_path: /health
    params:
      format: ['prometheus']

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.com/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration (for querying external data)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint.com/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
