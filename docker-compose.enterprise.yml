version: '3.8'

services:
  # Redis Cluster for high availability
  redis-master:
    image: redis:7-alpine
    container_name: redis-master
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --replica-read-only no
    volumes:
      - redis_master_data:/data
    networks:
      - tradingview_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  redis-replica:
    image: redis:7-alpine
    container_name: redis-replica
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --replicaof redis-master 6379
    depends_on:
      - redis-master
    volumes:
      - redis_replica_data:/data
    networks:
      - tradingview_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Specialized WebSocket Collectors
  forex-collector:
    build:
      context: .
      dockerfile: Dockerfile.collector
    container_name: forex-collector
    environment:
      - COLLECTOR_TYPE=forex
      - COLLECTOR_ID=forex_collector_1
      - REDIS_URL=redis://redis-master:6379
      - TRADINGVIEW_SESSION=${TRADINGVIEW_SESSION}
      - TRADINGVIEW_SIGNATURE=${TRADINGVIEW_SIGNATURE}
    depends_on:
      - redis-master
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  crypto-collector:
    build:
      context: .
      dockerfile: Dockerfile.collector
    container_name: crypto-collector
    environment:
      - COLLECTOR_TYPE=crypto
      - COLLECTOR_ID=crypto_collector_1
      - REDIS_URL=redis://redis-master:6379
      - TRADINGVIEW_SESSION=${TRADINGVIEW_SESSION}
      - TRADINGVIEW_SIGNATURE=${TRADINGVIEW_SIGNATURE}
    depends_on:
      - redis-master
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  indices-collector:
    build:
      context: .
      dockerfile: Dockerfile.collector
    container_name: indices-collector
    environment:
      - COLLECTOR_TYPE=indices
      - COLLECTOR_ID=indices_collector_1
      - REDIS_URL=redis://redis-master:6379
      - TRADINGVIEW_SESSION=${TRADINGVIEW_SESSION}
      - TRADINGVIEW_SIGNATURE=${TRADINGVIEW_SIGNATURE}
    depends_on:
      - redis-master
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Instances
  api-server-1:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: api-server-1
    ports:
      - "8001:8000"
    environment:
      - INSTANCE_ID=api_server_1
      - API_PORT=8000
      - REDIS_URLS=redis://redis-master:6379,redis://redis-replica:6379
      - API_KEYS=${API_KEYS}
      - ENABLE_WEBHOOKS=true
    depends_on:
      - redis-master
      - redis-replica
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-server-2:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: api-server-2
    ports:
      - "8002:8000"
    environment:
      - INSTANCE_ID=api_server_2
      - API_PORT=8000
      - REDIS_URLS=redis://redis-master:6379,redis://redis-replica:6379
      - API_KEYS=${API_KEYS}
      - ENABLE_WEBHOOKS=true
    depends_on:
      - redis-master
      - redis-replica
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-server-3:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: api-server-3
    ports:
      - "8003:8000"
    environment:
      - INSTANCE_ID=api_server_3
      - API_PORT=8000
      - REDIS_URLS=redis://redis-master:6379,redis://redis-replica:6379
      - API_KEYS=${API_KEYS}
      - ENABLE_WEBHOOKS=true
    depends_on:
      - redis-master
      - redis-replica
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NGINX Load Balancer
  nginx-lb:
    image: nginx:alpine
    container_name: nginx-lb
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api-server-1
      - api-server-2
      - api-server-3
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Webhook Service
  webhook-service:
    build:
      context: .
      dockerfile: Dockerfile.webhook
    container_name: webhook-service
    ports:
      - "9000:8000"
    environment:
      - REDIS_URL=redis://redis-master:6379
      - SERVICE_PORT=8000
    depends_on:
      - redis-master
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tradingview_network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - tradingview_network
    restart: unless-stopped

  # Redis Exporter for Prometheus
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis-master:6379
    depends_on:
      - redis-master
    networks:
      - tradingview_network
    restart: unless-stopped

  # Log aggregation
  loki:
    image: grafana/loki:latest
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - tradingview_network
    restart: unless-stopped

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    volumes:
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - tradingview_network
    restart: unless-stopped

networks:
  tradingview_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_master_data:
    driver: local
  redis_replica_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
