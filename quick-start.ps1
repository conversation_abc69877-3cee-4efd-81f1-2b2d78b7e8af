# Quick Start Script for TradingView API
# This script fixes common issues and starts a simplified version

param(
    [switch]$Clean,
    [switch]$Help
)

function Show-Help {
    @"
Quick Start Script for TradingView API

Usage: .\quick-start.ps1 [OPTIONS]

Options:
    -Clean      Clean up Docker resources before starting
    -Help       Show this help message

This script will:
1. Clean up any conflicting Docker networks
2. Start a simplified version of the TradingView API
3. Check service health

Services started:
- Redis (port 6379)
- API Server (port 8000)
- Collector (forex data)
- Prometheus (port 9090)
- Grafana (port 3000)

"@
}

if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🚀 TradingView API Quick Start" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Clean up if requested
if ($Clean) {
    Write-Host "🧹 Cleaning up Docker resources..." -ForegroundColor Yellow
    
    # Stop any running containers
    docker-compose -f docker-compose.simple.yml down 2>$null
    docker-compose -f docker-compose.enterprise.yml down 2>$null
    
    # Remove conflicting networks
    docker network rm tradingview-api-enterprise_tradingview_network 2>$null
    docker network rm tradingview_network 2>$null
    
    # Clean up unused resources
    docker system prune -f
    
    Write-Host "✅ Cleanup completed" -ForegroundColor Green
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  No .env file found. Creating from template..." -ForegroundColor Yellow
    
    if (Test-Path ".env.development") {
        Copy-Item ".env.development" ".env"
        Write-Host "✅ Created .env from development template" -ForegroundColor Green
    } else {
        Write-Host "❌ No environment template found. Please create .env file manually." -ForegroundColor Red
        exit 1
    }
}

# Check Docker
try {
    $null = docker --version
    Write-Host "✅ Docker is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed or not running" -ForegroundColor Red
    exit 1
}

# Check Docker Compose
try {
    $null = docker-compose --version
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not installed" -ForegroundColor Red
    exit 1
}

# Start the simplified stack
Write-Host "🚀 Starting TradingView API services..." -ForegroundColor Blue

try {
    # Start services
    docker-compose -f docker-compose.simple.yml up -d --build
    
    Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 30
    
    # Check service health
    Write-Host "🏥 Checking service health..." -ForegroundColor Blue
    
    $services = @{
        "Redis" = "http://localhost:6379"
        "API Server" = "http://localhost:8000/health"
        "Prometheus" = "http://localhost:9090"
        "Grafana" = "http://localhost:3000"
    }
    
    $healthyServices = 0
    $totalServices = $services.Count
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            if ($service.Key -eq "Redis") {
                # Special check for Redis
                $redisCheck = docker exec redis redis-cli ping 2>$null
                if ($redisCheck -eq "PONG") {
                    Write-Host "✅ $($service.Key) is healthy" -ForegroundColor Green
                    $healthyServices++
                } else {
                    Write-Host "❌ $($service.Key) is not responding" -ForegroundColor Red
                }
            } else {
                $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 5 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-Host "✅ $($service.Key) is healthy" -ForegroundColor Green
                    $healthyServices++
                } else {
                    Write-Host "❌ $($service.Key) returned status $($response.StatusCode)" -ForegroundColor Red
                }
            }
        } catch {
            Write-Host "❌ $($service.Key) is not responding" -ForegroundColor Red
        }
    }
    
    Write-Host "" -ForegroundColor White
    Write-Host "📊 Service Status: $healthyServices/$totalServices services healthy" -ForegroundColor Blue
    
    if ($healthyServices -eq $totalServices) {
        Write-Host "🎉 All services are running successfully!" -ForegroundColor Green
        Write-Host "" -ForegroundColor White
        Write-Host "🌐 Access your services:" -ForegroundColor Blue
        Write-Host "   • API Documentation: http://localhost:8000/docs" -ForegroundColor White
        Write-Host "   • API Health: http://localhost:8000/health" -ForegroundColor White
        Write-Host "   • Prometheus: http://localhost:9090" -ForegroundColor White
        Write-Host "   • Grafana: http://localhost:3000 (admin/admin)" -ForegroundColor White
        Write-Host "" -ForegroundColor White
        Write-Host "📝 Next steps:" -ForegroundColor Blue
        Write-Host "   1. Update your TradingView credentials in .env file" -ForegroundColor White
        Write-Host "   2. Test the API endpoints" -ForegroundColor White
        Write-Host "   3. Configure webhooks if needed" -ForegroundColor White
    } else {
        Write-Host "⚠️  Some services are not healthy. Check the logs:" -ForegroundColor Yellow
        Write-Host "   docker-compose -f docker-compose.simple.yml logs" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Failed to start services: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Try running with -Clean flag to reset everything" -ForegroundColor Yellow
    exit 1
}

Write-Host "" -ForegroundColor White
Write-Host "🔧 Useful commands:" -ForegroundColor Blue
Write-Host "   • View logs: docker-compose -f docker-compose.simple.yml logs -f" -ForegroundColor White
Write-Host "   • Stop services: docker-compose -f docker-compose.simple.yml down" -ForegroundColor White
Write-Host "   • Restart: .\quick-start.ps1 -Clean" -ForegroundColor White
