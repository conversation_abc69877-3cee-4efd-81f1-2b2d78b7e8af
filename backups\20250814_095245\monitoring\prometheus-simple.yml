# Simplified Prometheus Configuration
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API server monitoring
  - job_name: 'api-server'
    static_configs:
      - targets: ['api-server:8000']
    scrape_interval: 10s
    metrics_path: /metrics

  # Health checks
  - job_name: 'health-checks'
    static_configs:
      - targets: ['api-server:8000']
    scrape_interval: 30s
    metrics_path: /health
