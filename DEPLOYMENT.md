# TradingView API Enterprise - Deployment Guide

This guide covers the deployment and operation of the TradingView API Enterprise system.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Load Balancer  │    │   API Servers   │    │   Collectors    │
│     (NGINX)     │◄──►│   (FastAPI)     │◄──►│  (Specialized)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Monitoring    │    │     Redis       │    │   TradingView   │
│ (Prometheus)    │    │   (Cluster)     │    │      API        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

1. **Docker & Docker Compose**
   ```bash
   # Install Docker Desktop (Windows/Mac)
   # Or install Docker Engine + Docker Compose (Linux)
   docker --version
   docker-compose --version
   ```

2. **Environment Configuration**
   ```bash
   # Copy environment template
   cp .env.production .env
   
   # Edit with your actual values
   nano .env
   ```

3. **TradingView Credentials**
   - Get session token and signature from TradingView
   - Update `TRADINGVIEW_SESSION` and `TRADINGVIEW_SIGNATURE` in `.env`

### Deployment Commands

#### Linux/Mac (Bash)
```bash
# Make script executable
chmod +x deploy.sh

# Deploy to production
./deploy.sh deploy -e production

# Check health
./deploy.sh health

# View logs
./deploy.sh logs -s forex-collector
```

#### Windows (PowerShell)
```powershell
# Deploy to production
.\deploy.ps1 deploy -Environment production

# Check health
.\deploy.ps1 health

# View logs
.\deploy.ps1 logs -Service forex-collector
```

## 📋 Environment Configuration

### Production Environment (`.env.production`)

Key settings to configure:

```bash
# TradingView API Credentials
TRADINGVIEW_SESSION=your_session_token_here
TRADINGVIEW_SIGNATURE=your_signature_here

# API Security
API_KEYS=prod-key-123,prod-key-456,enterprise-key-789

# Redis Configuration
REDIS_URLS=redis://redis-master:6379,redis://redis-replica:6379
REDIS_PASSWORD=your_secure_redis_password

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring
GRAFANA_PASSWORD=your_secure_grafana_password
```

### Development Environment (`.env.development`)

For local development:

```bash
# Copy development template
cp .env.development .env

# Start development stack
docker-compose -f docker-compose.enterprise.yml -f docker-compose.override.yml up -d
```

## 🔧 Service Configuration

### Specialized Collectors

#### Forex Collector
- **Symbols**: Major, minor, and exotic currency pairs
- **Rate Limit**: 150ms between requests
- **Connections**: Up to 10 concurrent connections

#### Crypto Collector
- **Symbols**: Major cryptocurrencies, DeFi tokens, Layer 1 coins
- **Rate Limit**: 100ms between requests (higher volatility)
- **Features**: Volume spike detection, liquidation risk analysis

#### Indices Collector
- **Symbols**: Global stock indices (US, Europe, Asia-Pacific)
- **Rate Limit**: 200ms between requests
- **Features**: Market hours awareness, correlation tracking

#### Commodities Collector
- **Symbols**: Precious metals, energy, agriculture, livestock
- **Rate Limit**: 250ms between requests
- **Features**: Seasonal analysis, supply/demand tracking

### API Servers

- **Instances**: 3 load-balanced FastAPI servers
- **Features**: WebSocket streaming, REST API, webhook management
- **Health Checks**: Automatic failover and recovery

### Load Balancer (NGINX)

- **SSL Termination**: TLS 1.2/1.3 support
- **Rate Limiting**: API protection and abuse prevention
- **Health Checks**: Automatic backend health monitoring

## 📊 Monitoring & Observability

### Prometheus Metrics

Access metrics at: `http://localhost:9090`

Key metrics:
- `http_requests_total` - API request counts
- `market_data_messages_total` - Market data throughput
- `webhook_delivery_success_total` - Webhook delivery rates
- `collector_active_connections` - TradingView connections

### Grafana Dashboards

Access dashboards at: `http://localhost:3000`
- Username: `admin`
- Password: Set in `GRAFANA_PASSWORD`

Pre-configured dashboards:
- **System Overview**: Service health and performance
- **Market Data**: Real-time trading metrics
- **API Performance**: Request rates and response times
- **Infrastructure**: Resource utilization

### Log Aggregation (Loki)

Centralized logging with structured log parsing:
- Application logs from all services
- NGINX access and error logs
- System logs and container logs

## 🔒 Security

### SSL/TLS Configuration

1. **Generate SSL Certificates**
   ```bash
   # Self-signed for development
   mkdir -p ssl
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout ssl/key.pem -out ssl/cert.pem
   ```

2. **Production Certificates**
   - Use Let's Encrypt or commercial CA
   - Update paths in `nginx.conf`

### API Security

- **API Keys**: Multi-key authentication
- **Rate Limiting**: Per-IP and per-key limits
- **CORS**: Configurable origin restrictions
- **Request Validation**: Input sanitization

## 🔄 Operations

### Deployment Strategies

#### Blue-Green Deployment
```bash
# Deploy to staging environment
./deploy.sh deploy -e staging

# Test and validate
./deploy.sh health -e staging

# Switch to production
./deploy.sh deploy -e production
```

#### Rolling Updates
```bash
# Update specific service
./deploy.sh update -s api-server-1

# Update all API servers with zero downtime
./deploy.sh update
```

### Scaling

#### Horizontal Scaling
```bash
# Scale API servers
./deploy.sh scale -s api-server --replicas 5

# Scale collectors
docker-compose up -d --scale forex-collector=3
```

#### Vertical Scaling
Update resource limits in `docker-compose.enterprise.yml`:
```yaml
services:
  api-server-1:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
```

### Backup & Recovery

#### Automated Backups
```bash
# Create backup
./deploy.sh backup

# Restore from backup
./deploy.sh restore
```

#### Manual Backup
```bash
# Backup Redis data
docker exec redis-master redis-cli BGSAVE
docker cp redis-master:/data/dump.rdb ./backup/

# Backup configuration
tar -czf config-backup.tar.gz monitoring/ *.yml *.env
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
./deploy.sh logs -s service-name

# Check resource usage
docker stats

# Restart service
docker-compose restart service-name
```

#### High Memory Usage
```bash
# Check memory usage
docker stats --no-stream

# Scale down if needed
./deploy.sh scale -s api-server --replicas 2
```

#### Redis Connection Issues
```bash
# Check Redis status
docker-compose exec redis-master redis-cli ping

# Check Redis logs
./deploy.sh logs -s redis-master
```

#### TradingView Connection Failures
```bash
# Check collector logs
./deploy.sh logs -s forex-collector

# Verify credentials in .env file
# Check rate limiting settings
```

### Health Checks

#### System Health
```bash
# Overall system health
./deploy.sh health

# Individual service health
curl http://localhost:8000/health
```

#### Performance Monitoring
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090
- **API Docs**: http://localhost/docs

## 📈 Performance Tuning

### Database Optimization
- Redis memory optimization
- Connection pooling
- Query optimization

### Application Tuning
- Worker process scaling
- Memory management
- Caching strategies

### Network Optimization
- Connection keep-alive
- Compression settings
- CDN integration

## 🔧 Maintenance

### Regular Tasks

#### Daily
- Monitor system health
- Check error logs
- Verify backup completion

#### Weekly
- Review performance metrics
- Update security patches
- Clean up old logs

#### Monthly
- Capacity planning review
- Security audit
- Disaster recovery testing

### Updates

#### Security Updates
```bash
# Update base images
docker-compose pull

# Rebuild with latest patches
docker-compose build --no-cache

# Deploy updates
./deploy.sh update
```

#### Feature Updates
```bash
# Update application code
git pull origin main

# Build and deploy
./deploy.sh deploy -e production
```

## 📞 Support

### Monitoring Alerts

Configure alerts for:
- Service downtime
- High error rates
- Resource exhaustion
- Security incidents

### Contact Information

- **Operations Team**: <EMAIL>
- **Development Team**: <EMAIL>
- **Security Team**: <EMAIL>

### Documentation

- **API Documentation**: http://localhost/docs
- **Monitoring Runbooks**: ./monitoring/runbooks/
- **Architecture Diagrams**: ./docs/architecture/
