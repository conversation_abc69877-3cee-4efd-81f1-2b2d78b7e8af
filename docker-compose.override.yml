# Docker Compose Override for Development
# This file extends docker-compose.enterprise.yml for development use

version: '3.8'

services:
  # Development overrides for API servers
  api-server-1:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: builder  # Use builder stage for development
    volumes:
      - .:/app
      - /app/.venv  # Exclude virtual environment
    environment:
      - ENV=development
      - DEBUG=true
      - RELOAD=true
    command: ["python", "-m", "uvicorn", "enterprise_api_server:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    
  api-server-2:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: builder
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - ENV=development
      - DEBUG=true
      - RELOAD=true
    command: ["python", "-m", "uvicorn", "enterprise_api_server:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    
  api-server-3:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: builder
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - ENV=development
      - DEBUG=true
      - RELOAD=true
    command: ["python", "-m", "uvicorn", "enterprise_api_server:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # Development overrides for collectors
  forex-collector:
    build:
      context: .
      dockerfile: Dockerfile.collector
      target: builder
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=true
    command: ["node", "--inspect=0.0.0.0:9229", "start-collector.js"]
    ports:
      - "9229:9229"  # Debug port
    
  crypto-collector:
    build:
      context: .
      dockerfile: Dockerfile.collector
      target: builder
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=true
    command: ["node", "--inspect=0.0.0.0:9230", "start-collector.js"]
    ports:
      - "9230:9230"  # Debug port
    
  indices-collector:
    build:
      context: .
      dockerfile: Dockerfile.collector
      target: builder
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=true
    command: ["node", "--inspect=0.0.0.0:9231", "start-collector.js"]
    ports:
      - "9231:9231"  # Debug port

  # Development override for webhook service
  webhook-service:
    build:
      context: .
      dockerfile: Dockerfile.webhook
      target: builder
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - ENV=development
      - DEBUG=true
    command: ["python", "-m", "uvicorn", "enterprise_webhook_service:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # Development tools
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    hostname: redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis-master:6379
    networks:
      - tradingview_network
    depends_on:
      - redis-master

  # Database admin tool (if using PostgreSQL in future)
  # pgadmin:
  #   image: dpage/pgadmin4:latest
  #   container_name: pgadmin
  #   ports:
  #     - "5050:80"
  #   environment:
  #     - PGADMIN_DEFAULT_EMAIL=<EMAIL>
  #     - PGADMIN_DEFAULT_PASSWORD=admin
  #   networks:
  #     - tradingview_network

  # Development proxy for testing
  nginx-lb:
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Status page

# Development-specific networks
networks:
  tradingview_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Development volumes
volumes:
  redis_master_data:
    driver: local
  redis_replica_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
