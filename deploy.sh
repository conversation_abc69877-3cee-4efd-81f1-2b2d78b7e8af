#!/bin/bash

# TradingView API Enterprise Deployment Script
# Handles production deployment with zero-downtime rolling updates

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="tradingview-api-enterprise"
COMPOSE_FILE="docker-compose.enterprise.yml"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
TradingView API Enterprise Deployment Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    deploy          Deploy the entire stack
    update          Update services with zero downtime
    rollback        Rollback to previous version
    status          Show deployment status
    logs            Show service logs
    backup          Create backup before deployment
    restore         Restore from backup
    health          Check system health
    scale           Scale services
    stop            Stop all services
    start           Start all services
    restart         Restart all services
    clean           Clean up unused resources

Options:
    -e, --env       Environment (production|staging|development)
    -s, --service   Specific service to operate on
    -v, --version   Version tag for deployment
    -f, --force     Force operation without confirmation
    -h, --help      Show this help message

Examples:
    $0 deploy -e production
    $0 update -s api-server-1
    $0 scale -s api-server --replicas 5
    $0 logs -s forex-collector
    $0 health

EOF
}

# Parse command line arguments
COMMAND=""
ENVIRONMENT="production"
SERVICE=""
VERSION="latest"
FORCE=false
REPLICAS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        deploy|update|rollback|status|logs|backup|restore|health|scale|stop|start|restart|clean)
            COMMAND="$1"
            shift
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        --replicas)
            REPLICAS="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        production|staging|development)
            log_info "Environment: $ENVIRONMENT"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # Set environment file
    ENV_FILE=".env.$ENVIRONMENT"
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Environment file not found: $ENV_FILE"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check environment file
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Environment file not found: $ENV_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log_info "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup Redis data
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps redis-master | grep -q "Up"; then
        log_info "Backing up Redis data..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T redis-master redis-cli BGSAVE
        docker cp "$(docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps -q redis-master):/data/dump.rdb" "$BACKUP_DIR/redis-dump.rdb"
    fi
    
    # Backup configuration files
    cp -r monitoring "$BACKUP_DIR/"
    cp "$ENV_FILE" "$BACKUP_DIR/"
    cp "$COMPOSE_FILE" "$BACKUP_DIR/"
    
    # Backup logs
    if [[ -d "logs" ]]; then
        cp -r logs "$BACKUP_DIR/"
    fi
    
    log_success "Backup created: $BACKUP_DIR"
    echo "$BACKUP_DIR" > .last_backup
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local failed_services=()
    
    # Check each service
    services=("api-server-1" "api-server-2" "api-server-3" "forex-collector" "crypto-collector" "indices-collector" "commodities-collector" "webhook-service" "redis-master" "nginx-lb")
    
    for service in "${services[@]}"; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps "$service" | grep -q "Up"; then
            # Check health endpoint if available
            case $service in
                api-server-*|webhook-service)
                    if ! curl -f -s "http://localhost:8000/health" > /dev/null 2>&1; then
                        failed_services+=("$service")
                    fi
                    ;;
                *-collector)
                    if ! curl -f -s "http://localhost:3001/health" > /dev/null 2>&1; then
                        failed_services+=("$service")
                    fi
                    ;;
                nginx-lb)
                    if ! curl -f -s "http://localhost:80/health" > /dev/null 2>&1; then
                        failed_services+=("$service")
                    fi
                    ;;
            esac
            log_success "$service is healthy"
        else
            failed_services+=("$service")
            log_error "$service is not running"
        fi
    done
    
    if [[ ${#failed_services[@]} -eq 0 ]]; then
        log_success "All services are healthy"
        return 0
    else
        log_error "Failed services: ${failed_services[*]}"
        return 1
    fi
}

# Deploy function
deploy() {
    log_info "Starting deployment for environment: $ENVIRONMENT"
    
    # Create backup
    create_backup
    
    # Pull latest images
    log_info "Pulling latest images..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull
    
    # Build custom images
    log_info "Building custom images..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build
    
    # Start infrastructure services first
    log_info "Starting infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d redis-master redis-replica prometheus grafana loki
    
    # Wait for Redis to be ready
    log_info "Waiting for Redis to be ready..."
    timeout 60 bash -c 'until docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec redis-master redis-cli ping | grep -q PONG; do sleep 2; done'
    
    # Start collectors
    log_info "Starting collectors..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d forex-collector crypto-collector indices-collector commodities-collector
    
    # Start API servers
    log_info "Starting API servers..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d api-server-1 api-server-2 api-server-3
    
    # Start webhook service
    log_info "Starting webhook service..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d webhook-service
    
    # Start load balancer last
    log_info "Starting load balancer..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d nginx-lb
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Perform health check
    if health_check; then
        log_success "Deployment completed successfully!"
    else
        log_error "Deployment completed with errors. Check service logs."
        exit 1
    fi
}

# Update function with zero downtime
update() {
    log_info "Performing zero-downtime update..."
    
    if [[ -n "$SERVICE" ]]; then
        log_info "Updating service: $SERVICE"
        
        # Create backup
        create_backup
        
        # Update specific service
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull "$SERVICE"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d --no-deps "$SERVICE"
        
        # Wait and health check
        sleep 10
        if health_check; then
            log_success "Service $SERVICE updated successfully"
        else
            log_error "Service $SERVICE update failed"
            exit 1
        fi
    else
        # Rolling update of all services
        services=("api-server-1" "api-server-2" "api-server-3")
        
        for service in "${services[@]}"; do
            log_info "Updating $service..."
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull "$service"
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d --no-deps "$service"
            sleep 15  # Wait for service to be ready
            
            if ! health_check; then
                log_error "Update failed for $service"
                exit 1
            fi
        done
        
        log_success "Rolling update completed successfully"
    fi
}

# Scale function
scale() {
    if [[ -z "$SERVICE" || -z "$REPLICAS" ]]; then
        log_error "Service and replicas must be specified for scaling"
        exit 1
    fi
    
    log_info "Scaling $SERVICE to $REPLICAS replicas..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d --scale "$SERVICE=$REPLICAS"
    
    log_success "Scaled $SERVICE to $REPLICAS replicas"
}

# Show logs
show_logs() {
    if [[ -n "$SERVICE" ]]; then
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "$SERVICE"
    else
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
    fi
}

# Show status
show_status() {
    log_info "Service Status:"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    
    log_info "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Main execution
main() {
    if [[ -z "$COMMAND" ]]; then
        log_error "No command specified"
        show_help
        exit 1
    fi
    
    validate_environment
    check_prerequisites
    
    case $COMMAND in
        deploy)
            if [[ "$FORCE" == false && "$ENVIRONMENT" == "production" ]]; then
                read -p "Are you sure you want to deploy to production? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log_info "Deployment cancelled"
                    exit 0
                fi
            fi
            deploy
            ;;
        update)
            update
            ;;
        health)
            health_check
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        scale)
            scale
            ;;
        backup)
            create_backup
            ;;
        stop)
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop
            ;;
        start)
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" start
            ;;
        restart)
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart
            ;;
        clean)
            docker system prune -f
            docker volume prune -f
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
