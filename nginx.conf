# Enterprise NGINX Configuration for TradingView API Load Balancer
# Optimized for high-throughput financial data streaming

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimize for high connection loads
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format for load balancer analytics
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time" '
                    'upstream="$upstream_addr" host="$host"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;

    # Gzip compression for API responses
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        text/plain
        text/css
        text/xml
        text/javascript;

    # Rate limiting for API protection
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=websocket_limit:10m rate=50r/s;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # Upstream servers for FastAPI instances
    upstream fastapi_backend {
        # Load balancing method: least_conn for financial data
        least_conn;

        # FastAPI instances with health checks
        server api-server-1:8000 max_fails=3 fail_timeout=30s;
        server api-server-2:8000 max_fails=3 fail_timeout=30s;
        server api-server-3:8000 max_fails=3 fail_timeout=30s;

        # Keep-alive connections to backend
        keepalive 32;
    }

    # Upstream for WebSocket connections (sticky sessions)
    upstream websocket_backend {
        # IP hash for WebSocket session persistence
        ip_hash;

        server api-server-1:8000 max_fails=2 fail_timeout=10s;
        server api-server-2:8000 max_fails=2 fail_timeout=10s;
        server api-server-3:8000 max_fails=2 fail_timeout=10s;
    }

    # Upstream for webhook service
    upstream webhook_backend {
        server webhook-service:8000 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Main server block - HTTPS
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name tradingview-api.yourdomain.com;

        # SSL certificates (replace with your actual certificates)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Security headers for HTTPS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://fastapi_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Health check specific settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 5s;
            proxy_read_timeout 5s;

            access_log off; # Don't log health checks
        }

        # WebSocket endpoints with sticky sessions
        location /ws/ {
            # Rate limiting for WebSocket connections
            limit_req zone=websocket_limit burst=20 nodelay;
            limit_conn conn_limit_per_ip 10;

            proxy_pass http://websocket_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket specific timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s; # 5 minutes for long-lived connections

            # Disable buffering for real-time data
            proxy_buffering off;
            proxy_cache off;
        }

        # API endpoints with load balancing
        location /api/ {
            # Rate limiting for API calls
            limit_req zone=api_limit burst=50 nodelay;
            limit_conn conn_limit_per_ip 20;

            proxy_pass http://fastapi_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # API specific settings
            proxy_connect_timeout 10s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;

            # Enable caching for certain endpoints
            proxy_cache_bypass $http_pragma $http_authorization;
            proxy_no_cache $http_pragma $http_authorization;

            # CORS headers for API
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin * always;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
                add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type 'text/plain; charset=utf-8';
                add_header Content-Length 0;
                return 204;
            }
        }

        # Webhook service endpoints
        location /webhooks/ {
            proxy_pass http://webhook_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Webhook specific settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 15s;
        }

        # Documentation endpoints (Swagger/OpenAPI)
        location /docs {
            proxy_pass http://fastapi_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /redoc {
            proxy_pass http://fastapi_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /openapi.json {
            proxy_pass http://fastapi_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Cache OpenAPI spec for 1 hour
            proxy_cache_valid 200 1h;
        }

        # Static files (if any)
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Default location - redirect to docs
        location / {
            return 301 https://$server_name/docs;
        }

        # Custom error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;

        location = /404.html {
            root /var/www/error;
            internal;
        }

        location = /50x.html {
            root /var/www/error;
            internal;
        }
    }

    # HTTP server - redirect to HTTPS
    server {
        listen 80;
        listen [::]:80;
        server_name tradingview-api.yourdomain.com;

        # Health check endpoint for load balancer (HTTP)
        location /health {
            proxy_pass http://fastapi_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;
        }

        # Redirect all other traffic to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # Status page for monitoring
    server {
        listen 8080;
        server_name localhost;

        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16; # Docker network
            deny all;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}

# Stream block for TCP/UDP load balancing (if needed)
stream {
    # Log format for stream connections
    log_format stream_main '$remote_addr [$time_local] '
                          '$protocol $status $bytes_sent $bytes_received '
                          '$session_time "$upstream_addr" '
                          '"$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time"';

    access_log /var/log/nginx/stream_access.log stream_main;

    # Example: TCP load balancing for custom protocols
    # upstream tcp_backend {
    #     server api-server-1:9001;
    #     server api-server-2:9001;
    #     server api-server-3:9001;
    # }

    # server {
    #     listen 9001;
    #     proxy_pass tcp_backend;
    #     proxy_timeout 1s;
    #     proxy_responses 1;
    # }
}