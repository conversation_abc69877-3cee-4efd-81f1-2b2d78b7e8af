"""
Enterprise Redis Manager for TradingView API
Handles Redis Cluster, Streams, and Pub/Sub for enterprise-grade scaling
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Set, Any, Callable
from datetime import datetime, timedelta
import aioredis
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class MessageType(Enum):
    MARKET_DATA = "market_data"
    WEBHOOK_TRIGGER = "webhook_trigger"
    SYSTEM_ALERT = "system_alert"
    HEALTH_CHECK = "health_check"

@dataclass
class MarketDataMessage:
    symbol: str
    price: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    timestamp: str = None
    source: str = "tradingview"
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()

@dataclass
class WebhookMessage:
    webhook_id: str
    endpoint_url: str
    payload: Dict[str, Any]
    retry_count: int = 0
    max_retries: int = 3
    created_at: str = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.utcnow().isoformat()

class EnterpriseRedisManager:
    """
    Enterprise-grade Redis manager with clustering, streams, and pub/sub
    """
    
    def __init__(self, 
                 redis_urls: List[str] = None,
                 cluster_mode: bool = False,
                 stream_retention: int = 86400):  # 24 hours
        
        self.redis_urls = redis_urls or ["redis://localhost:6379"]
        self.cluster_mode = cluster_mode
        self.stream_retention = stream_retention
        
        # Redis connections
        self.redis_client: Optional[aioredis.Redis] = None
        self.redis_cluster: Optional[aioredis.RedisCluster] = None
        self.pubsub: Optional[aioredis.client.PubSub] = None
        
        # Connection pools for different purposes
        self.cache_client: Optional[aioredis.Redis] = None
        self.stream_client: Optional[aioredis.Redis] = None
        self.pubsub_client: Optional[aioredis.Redis] = None
        
        # Subscribers and handlers
        self.message_handlers: Dict[str, Callable] = {}
        self.active_subscriptions: Set[str] = set()
        
        # Performance metrics
        self.metrics = {
            "messages_published": 0,
            "messages_consumed": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "stream_entries": 0,
            "webhook_deliveries": 0,
            "failed_deliveries": 0
        }
        
        # Stream names
        self.MARKET_DATA_STREAM = "market_data_stream"
        self.WEBHOOK_STREAM = "webhook_stream"
        self.SYSTEM_EVENTS_STREAM = "system_events_stream"
        
    async def initialize(self) -> bool:
        """Initialize Redis connections with clustering support"""
        try:
            if self.cluster_mode and len(self.redis_urls) > 1:
                logger.info("🔗 Initializing Redis Cluster mode")
                self.redis_cluster = aioredis.RedisCluster.from_url(
                    self.redis_urls[0],
                    decode_responses=True,
                    skip_full_coverage_check=True
                )
                await self.redis_cluster.ping()
                self.redis_client = self.redis_cluster
                logger.info("✅ Redis Cluster connected")
            else:
                logger.info("🔗 Initializing Redis single-node mode")
                self.redis_client = aioredis.from_url(
                    self.redis_urls[0], 
                    decode_responses=True
                )
                await self.redis_client.ping()
                logger.info("✅ Redis single-node connected")
            
            # Create specialized connection pools
            await self._initialize_connection_pools()
            
            # Initialize streams
            await self._initialize_streams()
            
            # Start background tasks
            asyncio.create_task(self._stream_processor())
            asyncio.create_task(self._cleanup_expired_data())
            asyncio.create_task(self._metrics_reporter())
            
            logger.info("🚀 Enterprise Redis Manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Redis: {e}")
            return False
    
    async def _initialize_connection_pools(self):
        """Create specialized Redis connections for different purposes"""
        base_config = {"decode_responses": True}
        
        # Cache client - optimized for fast reads/writes
        self.cache_client = aioredis.from_url(
            self.redis_urls[0], 
            **base_config,
            max_connections=20
        )
        
        # Stream client - optimized for stream operations
        self.stream_client = aioredis.from_url(
            self.redis_urls[0], 
            **base_config,
            max_connections=10
        )
        
        # Pub/Sub client - dedicated for messaging
        self.pubsub_client = aioredis.from_url(
            self.redis_urls[0], 
            **base_config,
            max_connections=5
        )
        
        logger.info("✅ Redis connection pools initialized")
    
    async def _initialize_streams(self):
        """Initialize Redis Streams for event sourcing"""
        streams = [
            self.MARKET_DATA_STREAM,
            self.WEBHOOK_STREAM, 
            self.SYSTEM_EVENTS_STREAM
        ]
        
        for stream in streams:
            try:
                # Create stream if it doesn't exist
                await self.stream_client.xadd(stream, {"init": "stream_created"})
                
                # Set retention policy (keep last 24 hours)
                await self.stream_client.xtrim(
                    stream, 
                    maxlen=100000,  # Keep max 100k entries
                    approximate=True
                )
                
                logger.info(f"✅ Stream initialized: {stream}")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize stream {stream}: {e}")
    
    async def publish_market_data(self, data: MarketDataMessage) -> bool:
        """Publish market data to both cache and stream"""
        try:
            # Cache for fast access (30 second TTL)
            cache_key = f"market:{data.symbol}"
            await self.cache_client.setex(
                cache_key, 
                30, 
                json.dumps(asdict(data))
            )
            
            # Add to stream for event sourcing
            await self.stream_client.xadd(
                self.MARKET_DATA_STREAM,
                asdict(data)
            )
            
            # Publish to pub/sub for real-time subscribers
            await self.pubsub_client.publish(
                f"market_data:{data.symbol}",
                json.dumps(asdict(data))
            )
            
            self.metrics["messages_published"] += 1
            self.metrics["stream_entries"] += 1
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to publish market data for {data.symbol}: {e}")
            return False
    
    async def get_market_data(self, symbol: str) -> Optional[MarketDataMessage]:
        """Get latest market data from cache"""
        try:
            cache_key = f"market:{symbol}"
            cached_data = await self.cache_client.get(cache_key)
            
            if cached_data:
                self.metrics["cache_hits"] += 1
                data_dict = json.loads(cached_data)
                return MarketDataMessage(**data_dict)
            else:
                self.metrics["cache_misses"] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get market data for {symbol}: {e}")
            return None
    
    async def queue_webhook(self, webhook: WebhookMessage) -> bool:
        """Queue webhook for delivery"""
        try:
            await self.stream_client.xadd(
                self.WEBHOOK_STREAM,
                asdict(webhook)
            )
            
            logger.info(f"📤 Webhook queued: {webhook.webhook_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to queue webhook: {e}")
            return False
    
    async def subscribe_to_symbol(self, symbol: str, handler: Callable):
        """Subscribe to real-time updates for a symbol"""
        channel = f"market_data:{symbol}"
        
        if channel not in self.active_subscriptions:
            self.message_handlers[channel] = handler
            self.active_subscriptions.add(channel)
            
            # Start subscriber if not already running
            if not self.pubsub:
                self.pubsub = self.pubsub_client.pubsub()
                asyncio.create_task(self._pubsub_listener())
            
            await self.pubsub.subscribe(channel)
            logger.info(f"🔔 Subscribed to {symbol}")
    
    async def _pubsub_listener(self):
        """Listen for pub/sub messages"""
        try:
            async for message in self.pubsub.listen():
                if message["type"] == "message":
                    channel = message["channel"]
                    data = json.loads(message["data"])
                    
                    if channel in self.message_handlers:
                        handler = self.message_handlers[channel]
                        await handler(MarketDataMessage(**data))
                        
                    self.metrics["messages_consumed"] += 1
                    
        except Exception as e:
            logger.error(f"❌ Pub/sub listener error: {e}")
    
    async def _stream_processor(self):
        """Process webhook stream for delivery"""
        consumer_group = "webhook_processors"
        consumer_name = f"processor_{int(time.time())}"
        
        try:
            # Create consumer group
            await self.stream_client.xgroup_create(
                self.WEBHOOK_STREAM, 
                consumer_group, 
                id="0", 
                mkstream=True
            )
        except:
            pass  # Group already exists
        
        while True:
            try:
                # Read from stream
                messages = await self.stream_client.xreadgroup(
                    consumer_group,
                    consumer_name,
                    {self.WEBHOOK_STREAM: ">"},
                    count=10,
                    block=1000
                )
                
                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        webhook = WebhookMessage(**fields)
                        success = await self._deliver_webhook(webhook)
                        
                        if success:
                            # Acknowledge successful delivery
                            await self.stream_client.xack(
                                self.WEBHOOK_STREAM, 
                                consumer_group, 
                                msg_id
                            )
                            self.metrics["webhook_deliveries"] += 1
                        else:
                            self.metrics["failed_deliveries"] += 1
                            
            except Exception as e:
                logger.error(f"❌ Stream processor error: {e}")
                await asyncio.sleep(5)
    
    async def _deliver_webhook(self, webhook: WebhookMessage) -> bool:
        """Deliver webhook with retry logic"""
        import aiohttp
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook.endpoint_url,
                    json=webhook.payload,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ Webhook delivered: {webhook.webhook_id}")
                        return True
                    else:
                        logger.warning(f"⚠️ Webhook failed: {webhook.webhook_id} - Status: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Webhook delivery error: {webhook.webhook_id} - {e}")
            return False
    
    async def _cleanup_expired_data(self):
        """Clean up expired data periodically"""
        while True:
            try:
                # Clean up old stream entries
                cutoff_time = int((datetime.utcnow() - timedelta(hours=24)).timestamp() * 1000)
                
                for stream in [self.MARKET_DATA_STREAM, self.WEBHOOK_STREAM, self.SYSTEM_EVENTS_STREAM]:
                    await self.stream_client.xtrim(stream, minid=cutoff_time)
                
                logger.debug("🧹 Cleaned up expired stream data")
                
            except Exception as e:
                logger.error(f"❌ Cleanup error: {e}")
            
            await asyncio.sleep(3600)  # Run every hour
    
    async def _metrics_reporter(self):
        """Report metrics periodically"""
        while True:
            try:
                logger.info(f"📊 Redis Metrics: {self.metrics}")
                
                # Store metrics in Redis for monitoring
                await self.cache_client.setex(
                    "redis_metrics",
                    300,  # 5 minutes
                    json.dumps(self.metrics)
                )
                
            except Exception as e:
                logger.error(f"❌ Metrics reporting error: {e}")
            
            await asyncio.sleep(60)  # Report every minute
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        try:
            # Test Redis connectivity
            await self.redis_client.ping()
            
            # Get Redis info
            redis_info = await self.redis_client.info()
            
            return {
                "status": "healthy",
                "redis_connected": True,
                "cluster_mode": self.cluster_mode,
                "active_subscriptions": len(self.active_subscriptions),
                "metrics": self.metrics,
                "redis_info": {
                    "connected_clients": redis_info.get("connected_clients", 0),
                    "used_memory_human": redis_info.get("used_memory_human", "0B"),
                    "keyspace_hits": redis_info.get("keyspace_hits", 0),
                    "keyspace_misses": redis_info.get("keyspace_misses", 0)
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "redis_connected": False
            }
