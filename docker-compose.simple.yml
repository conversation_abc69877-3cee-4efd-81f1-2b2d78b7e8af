version: '3.8'

services:
  # Redis for caching and pub/sub
  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - tradingview_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Single API server for testing
  api-server:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: api-server
    ports:
      - "8000:8000"
    environment:
      - INSTANCE_ID=api_server_simple
      - REDIS_URLS=redis://redis:6379
      - API_KEYS=${API_KEYS:-test-key-123}
      - TRADINGVIEW_SESSION=${TRADINGVIEW_SESSION}
      - TRADINGVIEW_SIGNATURE=${TRADINGVIEW_SIGNATURE}
      - ENV=development
      - DEBUG=true
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tradingview_network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Collector disabled for now - will add back once API is working
  # collector:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.simple-collector
  #   container_name: collector
  #   environment:
  #     - COLLECTOR_TYPE=forex
  #     - COLLECTOR_ID=forex_collector_simple
  #     - REDIS_URL=redis://redis:6379
  #     - TRADINGVIEW_SESSION=${TRADINGVIEW_SESSION}
  #     - TRADINGVIEW_SIGNATURE=${TRADINGVIEW_SIGNATURE}
  #     - NODE_ENV=development
  #   depends_on:
  #     - redis
  #   networks:
  #     - tradingview_network
  #   restart: unless-stopped

  # Simple monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-simple.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tradingview_network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - tradingview_network
    restart: unless-stopped

networks:
  tradingview_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
