# Promtail Configuration for TradingView API Enterprise
# Collects logs from all services and forwards to Loki

server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Docker container logs
  - job_name: containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: containerlogs
          __path__: /var/lib/docker/containers/*/*log
    
    pipeline_stages:
      # Parse Docker JSON logs
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      
      # Extract container information
      - json:
          expressions:
            container_name: attrs.name
            image_name: attrs.image
          source: attrs
      
      # Add labels based on container name
      - labels:
          container_name:
          image_name:
          stream:
      
      # Parse application logs based on service
      - match:
          selector: '{container_name=~".*api-server.*"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) - (?P<level>\w+) - (?P<message>.*)'
            - labels:
                level:
            - timestamp:
                source: timestamp
                format: RFC3339Nano
      
      - match:
          selector: '{container_name=~".*collector.*"}'
          stages:
            - regex:
                expression: '\[(?P<collector_type>\w+)-COLLECTOR\] (?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) - (?P<message>.*)'
            - labels:
                collector_type:
            - timestamp:
                source: timestamp
                format: RFC3339Nano
      
      - match:
          selector: '{container_name=~".*webhook.*"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) - (?P<level>\w+) - (?P<message>.*)'
            - labels:
                level:
            - timestamp:
                source: timestamp
                format: RFC3339Nano

  # NGINX access logs
  - job_name: nginx-access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-access
          __path__: /var/log/nginx/access.log
    
    pipeline_stages:
      # Parse NGINX access log format
      - regex:
          expression: '(?P<remote_addr>\S+) - (?P<remote_user>\S+) \[(?P<timestamp>[^\]]+)\] "(?P<method>\S+) (?P<path>\S+) (?P<protocol>\S+)" (?P<status>\d+) (?P<body_bytes_sent>\d+) "(?P<http_referer>[^"]*)" "(?P<http_user_agent>[^"]*)" "(?P<http_x_forwarded_for>[^"]*)" rt=(?P<request_time>\S+) uct="(?P<upstream_connect_time>\S+)" uht="(?P<upstream_header_time>\S+)" urt="(?P<upstream_response_time>\S+)" upstream="(?P<upstream_addr>\S+)" host="(?P<host>\S+)"'
      
      # Add labels for filtering
      - labels:
          method:
          status:
          upstream_addr:
          host:
      
      # Parse timestamp
      - timestamp:
          source: timestamp
          format: 02/Jan/2006:15:04:05 -0700

  # NGINX error logs
  - job_name: nginx-error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-error
          __path__: /var/log/nginx/error.log
    
    pipeline_stages:
      # Parse NGINX error log format
      - regex:
          expression: '(?P<timestamp>\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}) \[(?P<level>\w+)\] (?P<pid>\d+)#(?P<tid>\d+): (?P<message>.*)'
      
      # Add labels
      - labels:
          level:
          pid:
      
      # Parse timestamp
      - timestamp:
          source: timestamp
          format: 2006/01/02 15:04:05

  # System logs (if available)
  - job_name: syslog
    static_configs:
      - targets:
          - localhost
        labels:
          job: syslog
          __path__: /var/log/syslog
    
    pipeline_stages:
      # Parse syslog format
      - regex:
          expression: '(?P<timestamp>\w{3} \d{1,2} \d{2}:\d{2}:\d{2}) (?P<hostname>\S+) (?P<program>\S+)(\[(?P<pid>\d+)\])?: (?P<message>.*)'
      
      # Add labels
      - labels:
          hostname:
          program:
          pid:
      
      # Parse timestamp (current year assumed)
      - timestamp:
          source: timestamp
          format: Jan 2 15:04:05

  # Application-specific log files
  - job_name: application-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: application-logs
          __path__: /app/logs/*.log
    
    pipeline_stages:
      # Extract filename as service label
      - regex:
          expression: '/app/logs/(?P<service>\w+)\.log'
          source: filename
      
      # Add service label
      - labels:
          service:
      
      # Parse JSON logs (if structured)
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            service: service
            trace_id: trace_id
      
      # Add labels from JSON
      - labels:
          level:
          service:
          trace_id:
      
      # Parse timestamp
      - timestamp:
          source: timestamp
          format: RFC3339Nano

# Limits to prevent resource exhaustion
limits_config:
  readline_rate: 10000
  readline_burst: 20000
