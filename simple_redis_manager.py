"""
Simple Redis Manager for TradingView API
Compatible with Python 3.11 and redis-py 5.x
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, asdict
import redis.asyncio as redis

logger = logging.getLogger(__name__)

@dataclass
class MarketDataMessage:
    symbol: str
    price: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    timestamp: str = None
    source: str = "tradingview"
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()

class SimpleRedisManager:
    """
    Simplified Redis manager compatible with Python 3.11
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub: Optional[redis.client.PubSub] = None
        
        # Metrics
        self.metrics = {
            "messages_published": 0,
            "messages_consumed": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0
        }
        
    async def initialize(self) -> bool:
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("✅ Redis connection established")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            return False
    
    async def publish_market_data(self, data: MarketDataMessage) -> bool:
        """Publish market data to Redis"""
        try:
            if not self.redis_client:
                logger.error("Redis client not initialized")
                return False
            
            # Cache for fast access (30 second TTL)
            cache_key = f"market:{data.symbol}"
            await self.redis_client.setex(
                cache_key, 
                30, 
                json.dumps(asdict(data))
            )
            
            # Publish to pub/sub for real-time subscribers
            channel = f"market_data:{data.symbol}"
            await self.redis_client.publish(
                channel,
                json.dumps(asdict(data))
            )
            
            self.metrics["messages_published"] += 1
            logger.debug(f"📡 Published market data for {data.symbol}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to publish market data for {data.symbol}: {e}")
            self.metrics["errors"] += 1
            return False
    
    async def get_market_data(self, symbol: str) -> Optional[MarketDataMessage]:
        """Get latest market data from cache"""
        try:
            if not self.redis_client:
                return None
                
            cache_key = f"market:{symbol}"
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                self.metrics["cache_hits"] += 1
                data_dict = json.loads(cached_data)
                return MarketDataMessage(**data_dict)
            else:
                self.metrics["cache_misses"] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get market data for {symbol}: {e}")
            self.metrics["errors"] += 1
            return None
    
    async def subscribe_to_symbol(self, symbol: str, handler):
        """Subscribe to real-time updates for a symbol"""
        try:
            if not self.redis_client:
                logger.error("Redis client not initialized")
                return False
                
            channel = f"market_data:{symbol}"
            
            if not self.pubsub:
                self.pubsub = self.redis_client.pubsub()
            
            await self.pubsub.subscribe(channel)
            logger.info(f"🔔 Subscribed to {symbol}")
            
            # Start listener task
            asyncio.create_task(self._pubsub_listener(handler))
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to subscribe to {symbol}: {e}")
            return False
    
    async def _pubsub_listener(self, handler):
        """Listen for pub/sub messages"""
        try:
            if not self.pubsub:
                return
                
            async for message in self.pubsub.listen():
                if message["type"] == "message":
                    try:
                        data = json.loads(message["data"])
                        market_data = MarketDataMessage(**data)
                        await handler(market_data)
                        self.metrics["messages_consumed"] += 1
                    except Exception as e:
                        logger.error(f"❌ Error processing message: {e}")
                        
        except Exception as e:
            logger.error(f"❌ Pub/sub listener error: {e}")
    
    async def store_metrics(self, service_name: str, metrics: Dict[str, Any]) -> bool:
        """Store service metrics"""
        try:
            if not self.redis_client:
                return False
                
            key = f"metrics:{service_name}"
            await self.redis_client.setex(
                key,
                300,  # 5 minutes TTL
                json.dumps(metrics)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to store metrics: {e}")
            return False
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get Redis health status"""
        try:
            if not self.redis_client:
                return {"status": "disconnected", "redis_connected": False}
            
            # Test connection
            await self.redis_client.ping()
            
            # Get Redis info
            info = await self.redis_client.info()
            
            return {
                "status": "healthy",
                "redis_connected": True,
                "metrics": self.metrics,
                "redis_info": {
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "0B"),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                    "uptime_in_seconds": info.get("uptime_in_seconds", 0)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "redis_connected": False
            }
    
    async def cleanup(self):
        """Clean up Redis connections"""
        try:
            if self.pubsub:
                await self.pubsub.close()
                
            if self.redis_client:
                await self.redis_client.close()
                
            logger.info("✅ Redis connections closed")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

# Global instance
redis_manager = SimpleRedisManager()

async def get_redis_manager() -> SimpleRedisManager:
    """Get the global Redis manager instance"""
    return redis_manager
