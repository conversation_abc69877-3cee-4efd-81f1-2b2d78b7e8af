"""
Simple TradingView API Server
Compatible with Python 3.11 and modern dependencies
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

import redis.asyncio as redis
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Environment variables
REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379")
API_KEYS = os.getenv("API_KEYS", "test-key-123").split(",")
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# Data models
class MarketDataRequest(BaseModel):
    symbols: List[str]
    timeframe: Optional[str] = "1m"

class MarketDataResponse(BaseModel):
    symbol: str
    price: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    timestamp: str
    source: str = "tradingview"

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    services: Dict[str, Any]
    version: str = "1.0.0"

# Global Redis client
redis_client: Optional[redis.Redis] = None

# FastAPI app
app = FastAPI(
    title="TradingView API Server",
    description="Simple TradingView API for market data",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Metrics storage
metrics = {
    "requests_total": 0,
    "requests_success": 0,
    "requests_error": 0,
    "redis_operations": 0,
    "start_time": time.time()
}

async def get_redis_client():
    """Get Redis client"""
    global redis_client
    if not redis_client:
        try:
            redis_client = redis.from_url(
                REDIS_URL,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            await redis_client.ping()
            logger.info("✅ Redis connection established")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            redis_client = None
    return redis_client

def verify_api_key(api_key: str = None):
    """Verify API key (simplified for demo)"""
    if DEBUG:
        return True  # Skip auth in debug mode
    return api_key in API_KEYS

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("🚀 Starting TradingView API Server...")
    
    # Initialize Redis
    await get_redis_client()
    
    # Store startup metrics
    if redis_client:
        try:
            await redis_client.setex(
                "api_server:startup",
                300,  # 5 minutes TTL
                json.dumps({
                    "timestamp": datetime.utcnow().isoformat(),
                    "status": "started"
                })
            )
        except Exception as e:
            logger.error(f"Failed to store startup metrics: {e}")
    
    logger.info("✅ TradingView API Server started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("🛑 Shutting down TradingView API Server...")
    
    if redis_client:
        try:
            await redis_client.close()
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
    
    logger.info("✅ Shutdown complete")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    metrics["requests_total"] += 1
    
    try:
        # Check Redis
        redis_status = "disconnected"
        redis_info = {}
        
        client = await get_redis_client()
        if client:
            try:
                await client.ping()
                redis_status = "connected"
                info = await client.info()
                redis_info = {
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "0B"),
                    "uptime_in_seconds": info.get("uptime_in_seconds", 0)
                }
                metrics["redis_operations"] += 1
            except Exception as e:
                redis_status = f"error: {str(e)}"
        
        # Calculate uptime
        uptime_seconds = time.time() - metrics["start_time"]
        
        response = HealthResponse(
            status="healthy" if redis_status == "connected" else "degraded",
            timestamp=datetime.utcnow().isoformat(),
            services={
                "redis": {
                    "status": redis_status,
                    "info": redis_info
                },
                "api_server": {
                    "status": "running",
                    "uptime_seconds": uptime_seconds,
                    "metrics": metrics
                }
            }
        )
        
        metrics["requests_success"] += 1
        return response
        
    except Exception as e:
        metrics["requests_error"] += 1
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/metrics")
async def get_metrics():
    """Prometheus-style metrics endpoint"""
    metrics["requests_total"] += 1
    
    try:
        uptime = time.time() - metrics["start_time"]
        
        prometheus_metrics = f"""# HELP api_requests_total Total number of API requests
# TYPE api_requests_total counter
api_requests_total {metrics["requests_total"]}

# HELP api_requests_success_total Total number of successful API requests
# TYPE api_requests_success_total counter
api_requests_success_total {metrics["requests_success"]}

# HELP api_requests_error_total Total number of failed API requests
# TYPE api_requests_error_total counter
api_requests_error_total {metrics["requests_error"]}

# HELP redis_operations_total Total number of Redis operations
# TYPE redis_operations_total counter
redis_operations_total {metrics["redis_operations"]}

# HELP api_server_uptime_seconds Server uptime in seconds
# TYPE api_server_uptime_seconds gauge
api_server_uptime_seconds {uptime}
"""
        
        metrics["requests_success"] += 1
        return JSONResponse(
            content=prometheus_metrics,
            media_type="text/plain"
        )
        
    except Exception as e:
        metrics["requests_error"] += 1
        logger.error(f"Metrics endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/market-data", response_model=List[MarketDataResponse])
async def get_market_data(request: MarketDataRequest, background_tasks: BackgroundTasks):
    """Get market data for symbols"""
    metrics["requests_total"] += 1
    
    try:
        client = await get_redis_client()
        responses = []
        
        for symbol in request.symbols:
            # Try to get from cache first
            cached_data = None
            if client:
                try:
                    cache_key = f"market:{symbol}"
                    cached_data = await client.get(cache_key)
                    metrics["redis_operations"] += 1
                except Exception as e:
                    logger.error(f"Redis cache error for {symbol}: {e}")
            
            if cached_data:
                # Use cached data
                data_dict = json.loads(cached_data)
                response = MarketDataResponse(**data_dict)
            else:
                # Generate mock data (in real implementation, this would fetch from TradingView)
                import random
                base_price = random.uniform(100, 1000)
                change = random.uniform(-5, 5)
                
                response = MarketDataResponse(
                    symbol=symbol,
                    price=round(base_price, 2),
                    bid=round(base_price - 0.01, 2),
                    ask=round(base_price + 0.01, 2),
                    volume=random.randint(1000, 100000),
                    change=round(change, 2),
                    change_percent=round((change / base_price) * 100, 2),
                    timestamp=datetime.utcnow().isoformat()
                )
                
                # Cache the data
                if client:
                    try:
                        cache_key = f"market:{symbol}"
                        await client.setex(
                            cache_key,
                            30,  # 30 seconds TTL
                            json.dumps(asdict(response))
                        )
                        metrics["redis_operations"] += 1
                    except Exception as e:
                        logger.error(f"Failed to cache data for {symbol}: {e}")
            
            responses.append(response)
        
        metrics["requests_success"] += 1
        return responses
        
    except Exception as e:
        metrics["requests_error"] += 1
        logger.error(f"Market data request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/symbols")
async def get_available_symbols():
    """Get list of available symbols"""
    metrics["requests_total"] += 1
    
    try:
        # Mock symbol list (in real implementation, this would come from TradingView)
        symbols = [
            "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD",
            "BTCUSD", "ETHUSD", "ADAUSD", "DOTUSD", "LINKUSD",
            "SPX500", "NAS100", "US30", "GER30", "UK100",
            "XAUUSD", "XAGUSD", "USOIL", "UKOIL", "NATGAS"
        ]
        
        metrics["requests_success"] += 1
        return {"symbols": symbols, "count": len(symbols)}
        
    except Exception as e:
        metrics["requests_error"] += 1
        logger.error(f"Symbols request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "simple_api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=DEBUG,
        log_level="info" if not DEBUG else "debug"
    )
