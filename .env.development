# TradingView API Enterprise - Development Environment Configuration
# Copy this file to .env for development

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
ENV=development
DEBUG=true
LOG_LEVEL=debug

# API Configuration
API_KEYS=dev-key-123,test-key-456
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# =============================================================================
# TRADINGVIEW CREDENTIALS
# =============================================================================
# Use your development/test credentials
TRADINGVIEW_SESSION=your_dev_session_token_here
TRADINGVIEW_SIGNATURE=your_dev_signature_here

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URLS=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20
REDIS_TIMEOUT=10

# =============================================================================
# COLLECTOR CONFIGURATION
# =============================================================================
# Forex Collector
FOREX_COLLECTOR_ID=forex_collector_dev_1
FOREX_RATE_LIMIT_DELAY=200  # Slower for development
FOREX_MAX_CONNECTIONS=5

# Crypto Collector
CRYPTO_COLLECTOR_ID=crypto_collector_dev_1
CRYPTO_RATE_LIMIT_DELAY=150
CRYPTO_MAX_CONNECTIONS=8
CRYPTO_VOLUME_SPIKE_THRESHOLD=1.5

# Indices Collector
INDICES_COLLECTOR_ID=indices_collector_dev_1
INDICES_RATE_LIMIT_DELAY=300
INDICES_MAX_CONNECTIONS=4

# Commodities Collector
COMMODITIES_COLLECTOR_ID=commodities_collector_dev_1
COMMODITIES_RATE_LIMIT_DELAY=400
COMMODITIES_MAX_CONNECTIONS=3

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
ENABLE_WEBHOOKS=true
WEBHOOK_MAX_RETRIES=2
WEBHOOK_TIMEOUT=5
WEBHOOK_RATE_LIMIT=50
WEBHOOK_CIRCUIT_BREAKER_THRESHOLD=3
WEBHOOK_CIRCUIT_BREAKER_TIMEOUT=30

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
JWT_SECRET_KEY=dev_jwt_secret_key_not_for_production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# CORS Configuration (permissive for development)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting (relaxed for development)
RATE_LIMIT_PER_MINUTE=10000
RATE_LIMIT_BURST=1000

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Prometheus
PROMETHEUS_METRICS_ENABLED=true
PROMETHEUS_METRICS_PORT=8000
PROMETHEUS_METRICS_PATH=/metrics

# Grafana
GRAFANA_PASSWORD=admin
GRAFANA_ADMIN_USER=admin

# Alerting (disabled for development)
ALERTMANAGER_WEBHOOK_URL=
ALERT_EMAIL_FROM=dev-alerts@localhost
ALERT_EMAIL_TO=developer@localhost

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FORMAT=text  # Easier to read in development
LOG_FILE_PATH=/app/logs/tradingview-api-dev.log
LOG_MAX_SIZE=50MB
LOG_BACKUP_COUNT=3
LOG_ROTATION=daily

# Structured logging
ENABLE_STRUCTURED_LOGGING=false
LOG_CORRELATION_ID=true
LOG_REQUEST_ID=true

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Python/FastAPI
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
UVICORN_WORKERS=1
UVICORN_LOOP=auto
UVICORN_HTTP=auto
UVICORN_RELOAD=true

# Node.js
NODE_ENV=development
NODE_OPTIONS=--max-old-space-size=1024
UV_THREADPOOL_SIZE=2

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=tradingview-api-dev
COMPOSE_FILE=docker-compose.enterprise.yml:docker-compose.override.yml

# Build arguments
BUILD_DATE=2024-01-15T10:30:00Z
VERSION=2.0.0-dev
VCS_REF=develop

# =============================================================================
# DEVELOPMENT FEATURES
# =============================================================================
ENABLE_DEBUG_ENDPOINTS=true
ENABLE_SWAGGER_UI=true
ENABLE_REDOC=true
ALLOW_ORIGINS_REGEX=true
ENABLE_HOT_RELOAD=true

# Mock services for development
ENABLE_MOCK_TRADINGVIEW=false
ENABLE_MOCK_WEBHOOKS=false
MOCK_DATA_ENABLED=false

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
TEST_DATABASE_URL=sqlite:///./test.db
TEST_REDIS_URL=redis://localhost:6379/1
ENABLE_TEST_ENDPOINTS=true
TEST_DATA_CLEANUP=true

# =============================================================================
# FEATURE FLAGS (all enabled for development)
# =============================================================================
ENABLE_MARKET_HOURS_FILTERING=true
ENABLE_VOLUME_SPIKE_DETECTION=true
ENABLE_SEASONAL_ANALYSIS=true
ENABLE_CORRELATION_TRACKING=true
ENABLE_CIRCUIT_BREAKERS=true
ENABLE_DEAD_LETTER_QUEUE=true

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Enable development tools
ENABLE_PROFILING=true
ENABLE_MEMORY_PROFILING=false
ENABLE_SQL_ECHO=false
ENABLE_REQUEST_LOGGING=true

# Development ports
DEV_API_PORT=8000
DEV_WEBHOOK_PORT=9000
DEV_METRICS_PORT=8001
DEV_DEBUG_PORT=5678

# =============================================================================
# LOCAL OVERRIDES
# =============================================================================
# These can be overridden in .env.local (not committed to git)
LOCAL_DEVELOPMENT=true
SKIP_AUTHENTICATION=false
BYPASS_RATE_LIMITING=false
