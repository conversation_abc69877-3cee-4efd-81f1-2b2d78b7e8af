{"dashboard": {"id": null, "title": "TradingView API - Enterprise Overview", "tags": ["tradingview", "api", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "{{job}} - {{instance}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "API Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{job}} - {{method}} {{status}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Market Data Messages", "type": "graph", "targets": [{"expr": "rate(market_data_messages_total[5m])", "legendFormat": "{{collector_type}} - {{symbol}}"}], "yAxes": [{"label": "Messages/sec", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Response Time Percentiles", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])", "legendFormat": "{{job}} - Error Rate"}], "yAxes": [{"label": "Error Rate", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "WebSocket Connections", "type": "stat", "targets": [{"expr": "websocket_active_connections", "legendFormat": "{{instance}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 1}, {"color": "green", "value": 10}]}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 7, "title": "Redis Memory Usage", "type": "stat", "targets": [{"expr": "redis_memory_used_bytes / redis_memory_max_bytes", "legendFormat": "Memory Usage"}], "fieldConfig": {"defaults": {"unit": "percentunit", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "red", "value": 0.9}]}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}}, {"id": 8, "title": "Webhook Delivery Success Rate", "type": "stat", "targets": [{"expr": "rate(webhook_delivery_success_total[5m]) / rate(webhook_delivery_attempts_total[5m])", "legendFormat": "Success Rate"}], "fieldConfig": {"defaults": {"unit": "percentunit", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.9}, {"color": "green", "value": 0.95}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 24}}, {"id": 9, "title": "Circuit Breaker Status", "type": "stat", "targets": [{"expr": "webhook_circuit_breaker_state", "legendFormat": "{{endpoint_id}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}, "mappings": [{"options": {"0": {"text": "CLOSED"}}, "type": "value"}, {"options": {"1": {"text": "OPEN"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 24}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up, instance)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "collector_type", "type": "query", "query": "label_values(collector_active_connections, collector_type)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(up[1m]) > 0", "titleFormat": "Service Restart", "textFormat": "{{instance}} restarted"}]}}}