{"permissions": {"allow": ["mcp__mcp-portal__context7_resolve-library-id", "mcp__mcp-portal__context7_get-library-docs", "Bash(npm install:*)", "mcp__mcp-portal__supabase_read_table_rows", "mcp__mcp-portal__supabase_create_table_records", "mcp__mcp-portal__supabase_update_table_records", "mcp__crawl4ai-rag__perform_rag_query", "Bash(find:*)", "Bash(git add:*)", "mcp__mcp-portal__github_get_file_contents", "mcp__mcp-portal__github_create_or_update_file", "mcp__serena", "Bash(rm:*)", "Bash(timeout 5m npm run build)", "<PERSON><PERSON>(timeout 15 npm run dev)", "WebFetch(domain:github.com)", "Bash(NODE_ENV=production npm run build)", "mcp__mcp-portal__github_list_commits", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "<PERSON><PERSON>(python3:*)", "mcp__mcp-portal__brave-search_brave_web_search"], "deny": []}}