"""
Enterprise Webhook Service for TradingView API
Handles webhook delivery with retry logic, circuit breakers, and monitoring
"""

import asyncio
import json
import logging
import time
import hashlib
import hmac
from typing import Dict, List, Optional, Set, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import aioredis
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, HttpUrl, Field

logger = logging.getLogger(__name__)

class WebhookStatus(Enum):
    PENDING = "pending"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRYING = "retrying"
    DEAD_LETTER = "dead_letter"

class WebhookPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class WebhookEndpoint:
    id: str
    url: str
    secret: Optional[str] = None
    max_retries: int = 3
    timeout: int = 10
    priority: WebhookPriority = WebhookPriority.NORMAL
    rate_limit: int = 100  # requests per minute
    active: bool = True
    created_at: str = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.utcnow().isoformat()

@dataclass
class WebhookDelivery:
    id: str
    endpoint_id: str
    payload: Dict[str, Any]
    status: WebhookStatus = WebhookStatus.PENDING
    attempts: int = 0
    max_retries: int = 3
    next_retry: Optional[str] = None
    created_at: str = None
    delivered_at: Optional[str] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.utcnow().isoformat()

class CircuitBreaker:
    """Circuit breaker for webhook endpoints"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call_succeeded(self):
        self.failure_count = 0
        self.state = "CLOSED"
    
    def call_failed(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
    
    def can_execute(self) -> bool:
        if self.state == "CLOSED":
            return True
        
        if self.state == "OPEN":
            if time.time() - self.last_failure_time >= self.recovery_timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        
        # HALF_OPEN state
        return True

class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self, max_tokens: int, refill_rate: int):
        self.max_tokens = max_tokens
        self.tokens = max_tokens
        self.refill_rate = refill_rate
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        now = time.time()
        
        # Refill tokens
        time_passed = now - self.last_refill
        tokens_to_add = int(time_passed * self.refill_rate / 60)  # per minute
        
        if tokens_to_add > 0:
            self.tokens = min(self.max_tokens, self.tokens + tokens_to_add)
            self.last_refill = now
        
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False

class EnterpriseWebhookService:
    """
    Enterprise-grade webhook service with retry logic, circuit breakers,
    rate limiting, and comprehensive monitoring
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[aioredis.Redis] = None
        
        # Webhook management
        self.endpoints: Dict[str, WebhookEndpoint] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.rate_limiters: Dict[str, RateLimiter] = {}
        
        # Delivery queues by priority
        self.delivery_queues = {
            WebhookPriority.CRITICAL: asyncio.Queue(),
            WebhookPriority.HIGH: asyncio.Queue(),
            WebhookPriority.NORMAL: asyncio.Queue(),
            WebhookPriority.LOW: asyncio.Queue()
        }
        
        # Dead letter queue
        self.dead_letter_queue: List[WebhookDelivery] = []
        
        # Worker management
        self.workers: List[asyncio.Task] = []
        self.is_running = False
        
        # Metrics
        self.metrics = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "failed_deliveries": 0,
            "retries": 0,
            "dead_letters": 0,
            "circuit_breaker_trips": 0,
            "rate_limit_hits": 0
        }
        
        # HTTP session for webhook delivery
        self.http_session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self) -> bool:
        """Initialize the webhook service"""
        try:
            # Initialize Redis
            self.redis_client = aioredis.from_url(
                self.redis_url, 
                decode_responses=True
            )
            await self.redis_client.ping()
            
            # Initialize HTTP session
            timeout = aiohttp.ClientTimeout(total=30)
            self.http_session = aiohttp.ClientSession(timeout=timeout)
            
            # Load existing endpoints from Redis
            await self.load_endpoints()
            
            # Start worker tasks
            await self.start_workers()
            
            # Start monitoring tasks
            asyncio.create_task(self.metrics_reporter())
            asyncio.create_task(self.dead_letter_processor())
            
            self.is_running = True
            logger.info("🚀 Enterprise Webhook Service initialized")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize webhook service: {e}")
            return False
    
    async def register_endpoint(self, endpoint: WebhookEndpoint) -> bool:
        """Register a new webhook endpoint"""
        try:
            self.endpoints[endpoint.id] = endpoint
            
            # Initialize circuit breaker and rate limiter
            self.circuit_breakers[endpoint.id] = CircuitBreaker()
            self.rate_limiters[endpoint.id] = RateLimiter(
                endpoint.rate_limit, 
                endpoint.rate_limit
            )
            
            # Store in Redis
            await self.redis_client.hset(
                "webhook_endpoints",
                endpoint.id,
                json.dumps(asdict(endpoint))
            )
            
            logger.info(f"✅ Registered webhook endpoint: {endpoint.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to register endpoint {endpoint.id}: {e}")
            return False
    
    async def queue_webhook(self, 
                          endpoint_id: str, 
                          payload: Dict[str, Any],
                          priority: WebhookPriority = WebhookPriority.NORMAL) -> str:
        """Queue a webhook for delivery"""
        try:
            if endpoint_id not in self.endpoints:
                raise ValueError(f"Unknown endpoint: {endpoint_id}")
            
            endpoint = self.endpoints[endpoint_id]
            if not endpoint.active:
                raise ValueError(f"Endpoint {endpoint_id} is inactive")
            
            # Create delivery record
            delivery_id = f"wh_{int(time.time() * 1000)}_{endpoint_id}"
            delivery = WebhookDelivery(
                id=delivery_id,
                endpoint_id=endpoint_id,
                payload=payload,
                max_retries=endpoint.max_retries
            )
            
            # Add to appropriate priority queue
            await self.delivery_queues[priority].put(delivery)
            
            # Store in Redis for persistence
            await self.redis_client.hset(
                "webhook_deliveries",
                delivery_id,
                json.dumps(asdict(delivery))
            )
            
            logger.info(f"📤 Queued webhook {delivery_id} for {endpoint_id}")
            return delivery_id
            
        except Exception as e:
            logger.error(f"❌ Failed to queue webhook: {e}")
            raise
    
    async def start_workers(self):
        """Start webhook delivery workers"""
        # Start workers for each priority level
        worker_counts = {
            WebhookPriority.CRITICAL: 3,
            WebhookPriority.HIGH: 2,
            WebhookPriority.NORMAL: 2,
            WebhookPriority.LOW: 1
        }
        
        for priority, count in worker_counts.items():
            for i in range(count):
                worker = asyncio.create_task(
                    self.delivery_worker(priority, f"{priority.name.lower()}_{i}")
                )
                self.workers.append(worker)
        
        logger.info(f"🔧 Started {len(self.workers)} webhook delivery workers")
    
    async def delivery_worker(self, priority: WebhookPriority, worker_id: str):
        """Worker that processes webhook deliveries"""
        queue = self.delivery_queues[priority]
        
        while self.is_running:
            try:
                # Get delivery from queue
                delivery = await queue.get()
                
                # Process delivery
                success = await self.deliver_webhook(delivery)
                
                if not success and delivery.attempts < delivery.max_retries:
                    # Schedule retry
                    await self.schedule_retry(delivery)
                elif not success:
                    # Move to dead letter queue
                    await self.move_to_dead_letter(delivery)
                
                # Mark task as done
                queue.task_done()
                
            except Exception as e:
                logger.error(f"❌ Worker {worker_id} error: {e}")
                await asyncio.sleep(1)
    
    async def deliver_webhook(self, delivery: WebhookDelivery) -> bool:
        """Deliver a webhook with circuit breaker and rate limiting"""
        try:
            endpoint = self.endpoints[delivery.endpoint_id]
            circuit_breaker = self.circuit_breakers[delivery.endpoint_id]
            rate_limiter = self.rate_limiters[delivery.endpoint_id]
            
            # Check circuit breaker
            if not circuit_breaker.can_execute():
                logger.warning(f"🚨 Circuit breaker OPEN for {delivery.endpoint_id}")
                self.metrics["circuit_breaker_trips"] += 1
                return False
            
            # Check rate limit
            if not rate_limiter.consume():
                logger.warning(f"⏰ Rate limit hit for {delivery.endpoint_id}")
                self.metrics["rate_limit_hits"] += 1
                # Re-queue for later
                await asyncio.sleep(1)
                await self.delivery_queues[endpoint.priority].put(delivery)
                return True  # Don't count as failure
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "TradingView-Enterprise-Webhook/1.0"
            }
            
            # Add signature if secret is configured
            if endpoint.secret:
                payload_str = json.dumps(delivery.payload, sort_keys=True)
                signature = hmac.new(
                    endpoint.secret.encode(),
                    payload_str.encode(),
                    hashlib.sha256
                ).hexdigest()
                headers["X-Webhook-Signature"] = f"sha256={signature}"
            
            # Deliver webhook
            delivery.attempts += 1
            start_time = time.time()
            
            async with self.http_session.post(
                endpoint.url,
                json=delivery.payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=endpoint.timeout)
            ) as response:
                
                response_time = time.time() - start_time
                
                if response.status == 200:
                    # Success
                    delivery.status = WebhookStatus.DELIVERED
                    delivery.delivered_at = datetime.utcnow().isoformat()
                    
                    circuit_breaker.call_succeeded()
                    
                    self.metrics["successful_deliveries"] += 1
                    self.metrics["total_deliveries"] += 1
                    
                    logger.info(f"✅ Delivered webhook {delivery.id} in {response_time:.2f}s")
                    
                    # Update Redis
                    await self.redis_client.hset(
                        "webhook_deliveries",
                        delivery.id,
                        json.dumps(asdict(delivery))
                    )
                    
                    return True
                else:
                    # HTTP error
                    error_msg = f"HTTP {response.status}: {await response.text()}"
                    delivery.error_message = error_msg
                    
                    circuit_breaker.call_failed()
                    
                    logger.error(f"❌ Webhook {delivery.id} failed: {error_msg}")
                    return False
                    
        except asyncio.TimeoutError:
            delivery.error_message = "Request timeout"
            circuit_breaker.call_failed()
            logger.error(f"⏰ Webhook {delivery.id} timed out")
            return False
            
        except Exception as e:
            delivery.error_message = str(e)
            circuit_breaker.call_failed()
            logger.error(f"❌ Webhook {delivery.id} error: {e}")
            return False
    
    async def schedule_retry(self, delivery: WebhookDelivery):
        """Schedule a webhook for retry with exponential backoff"""
        try:
            # Calculate retry delay (exponential backoff)
            delay = min(300, 2 ** delivery.attempts * 5)  # Max 5 minutes
            retry_time = datetime.utcnow() + timedelta(seconds=delay)
            
            delivery.status = WebhookStatus.RETRYING
            delivery.next_retry = retry_time.isoformat()
            
            # Update Redis
            await self.redis_client.hset(
                "webhook_deliveries",
                delivery.id,
                json.dumps(asdict(delivery))
            )
            
            # Schedule retry
            asyncio.create_task(self.delayed_retry(delivery, delay))
            
            self.metrics["retries"] += 1
            logger.info(f"🔄 Scheduled retry for {delivery.id} in {delay}s")
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule retry for {delivery.id}: {e}")
    
    async def delayed_retry(self, delivery: WebhookDelivery, delay: int):
        """Execute delayed retry"""
        await asyncio.sleep(delay)
        
        endpoint = self.endpoints[delivery.endpoint_id]
        await self.delivery_queues[endpoint.priority].put(delivery)
    
    async def move_to_dead_letter(self, delivery: WebhookDelivery):
        """Move failed delivery to dead letter queue"""
        try:
            delivery.status = WebhookStatus.DEAD_LETTER
            self.dead_letter_queue.append(delivery)
            
            # Store in Redis dead letter queue
            await self.redis_client.lpush(
                "webhook_dead_letter_queue",
                json.dumps(asdict(delivery))
            )
            
            self.metrics["dead_letters"] += 1
            self.metrics["failed_deliveries"] += 1
            self.metrics["total_deliveries"] += 1
            
            logger.error(f"💀 Moved {delivery.id} to dead letter queue")
            
        except Exception as e:
            logger.error(f"❌ Failed to move {delivery.id} to dead letter: {e}")
    
    async def load_endpoints(self):
        """Load webhook endpoints from Redis"""
        try:
            endpoints_data = await self.redis_client.hgetall("webhook_endpoints")
            
            for endpoint_id, endpoint_json in endpoints_data.items():
                endpoint_dict = json.loads(endpoint_json)
                endpoint = WebhookEndpoint(**endpoint_dict)
                
                self.endpoints[endpoint_id] = endpoint
                self.circuit_breakers[endpoint_id] = CircuitBreaker()
                self.rate_limiters[endpoint_id] = RateLimiter(
                    endpoint.rate_limit,
                    endpoint.rate_limit
                )
            
            logger.info(f"📥 Loaded {len(self.endpoints)} webhook endpoints")
            
        except Exception as e:
            logger.error(f"❌ Failed to load endpoints: {e}")
    
    async def metrics_reporter(self):
        """Report metrics periodically"""
        while self.is_running:
            try:
                # Calculate additional metrics
                total_endpoints = len(self.endpoints)
                active_endpoints = sum(1 for ep in self.endpoints.values() if ep.active)
                
                circuit_breaker_status = {
                    ep_id: cb.state 
                    for ep_id, cb in self.circuit_breakers.items()
                }
                
                metrics_report = {
                    **self.metrics,
                    "total_endpoints": total_endpoints,
                    "active_endpoints": active_endpoints,
                    "dead_letter_queue_size": len(self.dead_letter_queue),
                    "circuit_breaker_status": circuit_breaker_status,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                # Store metrics in Redis
                await self.redis_client.setex(
                    "webhook_service_metrics",
                    300,  # 5 minutes TTL
                    json.dumps(metrics_report)
                )
                
                logger.info(f"📊 Webhook Metrics: {json.dumps(metrics_report, indent=2)}")
                
            except Exception as e:
                logger.error(f"❌ Metrics reporting error: {e}")
            
            await asyncio.sleep(60)  # Report every minute
    
    async def dead_letter_processor(self):
        """Process dead letter queue periodically"""
        while self.is_running:
            try:
                # Process dead letters every 5 minutes
                await asyncio.sleep(300)
                
                if not self.dead_letter_queue:
                    continue
                
                logger.info(f"🔄 Processing {len(self.dead_letter_queue)} dead letters")
                
                # Try to reprocess dead letters (manual intervention might have fixed issues)
                for delivery in self.dead_letter_queue[:]:
                    endpoint = self.endpoints.get(delivery.endpoint_id)
                    if endpoint and endpoint.active:
                        # Reset attempts and try again
                        delivery.attempts = 0
                        delivery.status = WebhookStatus.PENDING
                        
                        await self.delivery_queues[endpoint.priority].put(delivery)
                        self.dead_letter_queue.remove(delivery)
                        
                        logger.info(f"🔄 Reprocessing dead letter: {delivery.id}")
                
            except Exception as e:
                logger.error(f"❌ Dead letter processor error: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive service status"""
        return {
            "is_running": self.is_running,
            "metrics": self.metrics,
            "endpoints": len(self.endpoints),
            "active_endpoints": sum(1 for ep in self.endpoints.values() if ep.active),
            "dead_letter_queue_size": len(self.dead_letter_queue),
            "circuit_breakers": {
                ep_id: {
                    "state": cb.state,
                    "failure_count": cb.failure_count
                }
                for ep_id, cb in self.circuit_breakers.items()
            },
            "queue_sizes": {
                priority.name: queue.qsize()
                for priority, queue in self.delivery_queues.items()
            }
        }
    
    async def shutdown(self):
        """Gracefully shutdown the webhook service"""
        try:
            logger.info("🛑 Shutting down webhook service...")
            
            self.is_running = False
            
            # Cancel all workers
            for worker in self.workers:
                worker.cancel()
            
            # Wait for workers to finish
            await asyncio.gather(*self.workers, return_exceptions=True)
            
            # Close HTTP session
            if self.http_session:
                await self.http_session.close()
            
            # Close Redis connection
            if self.redis_client:
                await self.redis_client.close()
            
            logger.info("✅ Webhook service shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Shutdown error: {e}")

# FastAPI integration
webhook_service = EnterpriseWebhookService()

app = FastAPI(title="Enterprise Webhook Service")

@app.on_event("startup")
async def startup():
    await webhook_service.initialize()

@app.on_event("shutdown") 
async def shutdown():
    await webhook_service.shutdown()

@app.get("/health")
async def health_check():
    return webhook_service.get_status()

@app.post("/webhooks/register")
async def register_webhook(endpoint: WebhookEndpoint):
    success = await webhook_service.register_endpoint(endpoint)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to register endpoint")
    return {"status": "registered", "endpoint_id": endpoint.id}

@app.post("/webhooks/send")
async def send_webhook(endpoint_id: str, payload: dict, priority: str = "normal"):
    try:
        priority_enum = WebhookPriority[priority.upper()]
        delivery_id = await webhook_service.queue_webhook(endpoint_id, payload, priority_enum)
        return {"status": "queued", "delivery_id": delivery_id}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
