/**
 * 🥇 COMMODITIES COLLECTOR - Enterprise WebSocket Service
 * 
 * Specialized collector for commodities with optimized rate limiting
 * and connection management specifically for precious metals, energy, and agricultural products.
 * 
 * Features:
 * - Dedicated to COMMODITIES symbols only
 * - Optimized rate limiting for commodity markets
 * - Session management for metals, energy, and agriculture
 * - Economic event correlation and seasonal patterns
 * - Supply/demand analysis integration
 */

const TradingView = require('../main');
const { EnterpriseRedisManager } = require('./enterprise_redis_client');
const EventEmitter = require('events');

class CommoditiesCollector extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            maxConnections: 6, // Fewer connections for commodities
            rateLimitDelay: 250, // 250ms between requests (commodities-optimized)
            reconnectDelay: 5000,
            healthCheckInterval: 45000, // Less frequent for commodities
            redisUrl: config.redisUrl || 'redis://localhost:6379',
            collectorId: config.collectorId || 'commodities_collector_1',
            seasonalAnalysis: true, // Enable seasonal pattern analysis
            supplyDemandTracking: true, // Track supply/demand indicators
            ...config
        };
        
        // Commodities-specific symbol categories
        this.symbolCategories = {
            precious_metals: {
                symbols: ['GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM'],
                characteristics: {
                    volatility: 'medium',
                    seasonality: 'low',
                    economicSensitivity: 'high',
                    inflationHedge: true
                }
            },
            energy: {
                symbols: ['CRUDE_OIL_WTI', 'BRENT_OIL', 'NATURAL_GAS', 'HEATING_OIL'],
                characteristics: {
                    volatility: 'high',
                    seasonality: 'high',
                    economicSensitivity: 'very_high',
                    geopoliticalSensitivity: 'very_high'
                }
            },
            base_metals: {
                symbols: ['COPPER', 'ALUMINUM', 'ZINC', 'NICKEL', 'LEAD'],
                characteristics: {
                    volatility: 'medium',
                    seasonality: 'medium',
                    economicSensitivity: 'very_high',
                    industrialDemand: true
                }
            },
            agriculture: {
                symbols: ['WHEAT', 'CORN', 'SOYBEANS', 'SUGAR', 'COFFEE', 'COTTON'],
                characteristics: {
                    volatility: 'high',
                    seasonality: 'very_high',
                    weatherSensitivity: 'very_high',
                    cropCycles: true
                }
            },
            livestock: {
                symbols: ['LIVE_CATTLE', 'FEEDER_CATTLE', 'LEAN_HOGS'],
                characteristics: {
                    volatility: 'medium',
                    seasonality: 'high',
                    diseaseRisk: 'high',
                    feedCostSensitivity: 'high'
                }
            }
        };
        
        // Connection management
        this.connections = new Map();
        this.subscriptions = new Map();
        this.connectionHealth = new Map();
        this.isRunning = false;
        
        // Commodities-specific features
        this.seasonalPatterns = new Map(); // Track seasonal price patterns
        this.supplyDemandIndicators = new Map(); // Track supply/demand metrics
        this.economicCorrelations = new Map(); // Track economic indicator correlations
        this.weatherImpacts = new Map(); // Track weather-related price impacts
        this.inventoryLevels = new Map(); // Track inventory/storage levels
        
        // Rate limiting
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.lastRequestTime = 0;
        
        // Redis integration
        this.redisManager = null;
        
        // Performance metrics
        this.metrics = {
            totalSymbols: 0,
            activeConnections: 0,
            messagesPublished: 0,
            seasonalAlerts: 0,
            supplyDemandAlerts: 0,
            economicCorrelations: 0,
            weatherAlerts: 0,
            errors: 0,
            reconnections: 0,
            startTime: Date.now()
        };
        
        this.logger = {
            info: (msg) => console.log(`[COMMODITIES-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            error: (msg) => console.error(`[COMMODITIES-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            warn: (msg) => console.warn(`[COMMODITIES-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            debug: (msg) => console.log(`[COMMODITIES-COLLECTOR-DEBUG] ${new Date().toISOString()} - ${msg}`)
        };
    }
    
    async initialize() {
        try {
            this.logger.info('🚀 Initializing Commodities Collector...');
            
            // Initialize Redis connection
            this.redisManager = new EnterpriseRedisManager({
                redisUrl: this.config.redisUrl,
                collectorId: this.config.collectorId
            });
            
            await this.redisManager.initialize();
            
            // Initialize seasonal patterns
            this.initializeSeasonalPatterns();
            
            // Create initial connections for different commodity categories
            await this.createCategoryConnections();
            
            // Start background tasks
            this.startHealthMonitoring();
            this.startMetricsReporting();
            this.startSeasonalAnalysis();
            this.startSupplyDemandTracking();
            
            this.isRunning = true;
            this.logger.info('✅ Commodities Collector initialized successfully');
            
            return true;
            
        } catch (error) {
            this.logger.error(`❌ Failed to initialize Commodities Collector: ${error.message}`);
            throw error;
        }
    }
    
    initializeSeasonalPatterns() {
        // Initialize seasonal patterns for different commodities
        const seasonalData = {
            'GOLD': { strongMonths: [8, 9, 10, 11], weakMonths: [3, 4, 5] }, // Aug-Nov strong, Mar-May weak
            'SILVER': { strongMonths: [9, 10, 11, 12], weakMonths: [6, 7, 8] }, // Sep-Dec strong, Jun-Aug weak
            'CRUDE_OIL_WTI': { strongMonths: [4, 5, 6], weakMonths: [11, 12, 1] }, // Apr-Jun strong (driving season)
            'NATURAL_GAS': { strongMonths: [10, 11, 12, 1], weakMonths: [4, 5, 6] }, // Oct-Jan strong (heating season)
            'WHEAT': { strongMonths: [6, 7, 8], weakMonths: [10, 11, 12] }, // Jun-Aug strong (harvest concerns)
            'CORN': { strongMonths: [6, 7, 8], weakMonths: [10, 11, 12] }, // Jun-Aug strong (growing season)
            'SOYBEANS': { strongMonths: [7, 8, 9], weakMonths: [11, 12, 1] }, // Jul-Sep strong (weather critical)
            'COFFEE': { strongMonths: [7, 8, 9], weakMonths: [12, 1, 2] }, // Jul-Sep strong (Brazil frost season)
            'SUGAR': { strongMonths: [4, 5, 6], weakMonths: [12, 1, 2] }, // Apr-Jun strong (Brazil harvest)
            'COPPER': { strongMonths: [1, 2, 3], weakMonths: [7, 8, 9] } // Jan-Mar strong (China demand)
        };
        
        for (const [symbol, pattern] of Object.entries(seasonalData)) {
            this.seasonalPatterns.set(symbol, {
                ...pattern,
                currentSeason: this.getCurrentSeason(pattern),
                lastUpdate: Date.now()
            });
        }
    }
    
    getCurrentSeason(pattern) {
        const currentMonth = new Date().getMonth() + 1; // 1-12
        
        if (pattern.strongMonths.includes(currentMonth)) {
            return 'strong';
        } else if (pattern.weakMonths.includes(currentMonth)) {
            return 'weak';
        } else {
            return 'neutral';
        }
    }
    
    async createCategoryConnections() {
        const categories = Object.keys(this.symbolCategories);
        
        for (const category of categories) {
            try {
                const connectionId = `commodities_${category}`;
                const client = this.createTradingViewClient();
                
                this.connections.set(connectionId, {
                    client,
                    category,
                    symbols: new Set(),
                    lastActivity: Date.now(),
                    errorCount: 0,
                    characteristics: this.symbolCategories[category].characteristics
                });
                
                this.connectionHealth.set(connectionId, {
                    status: 'healthy',
                    lastCheck: Date.now(),
                    successRate: 100,
                    avgResponseTime: 0,
                    category,
                    commodityType: category
                });
                
                this.logger.info(`🔗 Created connection for ${category} commodities: ${connectionId}`);
                
            } catch (error) {
                this.logger.error(`❌ Failed to create connection for ${category}: ${error.message}`);
            }
        }
        
        this.metrics.activeConnections = this.connections.size;
    }
    
    createTradingViewClient() {
        const session = process.env.TRADINGVIEW_SESSION;
        const signature = process.env.TRADINGVIEW_SIGNATURE;
        
        if (session && signature) {
            return new TradingView.Client({
                token: session,
                signature: signature
            });
        } else {
            return new TradingView.Client();
        }
    }
    
    async subscribeToSymbol(symbol, category = null) {
        try {
            // Auto-detect category if not provided
            if (!category) {
                category = this.detectSymbolCategory(symbol);
            }
            
            const connectionId = `commodities_${category}`;
            const connection = this.connections.get(connectionId);
            
            if (!connection) {
                throw new Error(`No connection available for category: ${category}`);
            }
            
            // Check if already subscribed
            if (this.subscriptions.has(symbol)) {
                this.logger.warn(`⚠️ Already subscribed to ${symbol}`);
                return;
            }
            
            const client = connection.client;
            const quoteSession = new client.Session.Quote({ fields: 'all' });
            const market = new quoteSession.Market(symbol);
            
            // Set up real-time data handler
            market.onData((quoteData) => {
                this.handleMarketData(symbol, quoteData, connectionId, category);
            });
            
            market.onLoaded(() => {
                this.logger.info(`✅ Market loaded: ${symbol} (${category})`);
            });
            
            market.onError((...errorArgs) => {
                const errorMessage = errorArgs.join(' ');
                this.logger.error(`❌ Market error for ${symbol}: ${errorMessage}`);
                this.handleConnectionError(connectionId, symbol, errorMessage);
            });
            
            // Track subscription
            this.subscriptions.set(symbol, {
                market,
                quoteSession,
                connectionId,
                category,
                subscribedAt: Date.now(),
                characteristics: connection.characteristics
            });
            
            connection.symbols.add(symbol);
            connection.lastActivity = Date.now();
            
            // Initialize commodity-specific tracking
            this.supplyDemandIndicators.set(symbol, {
                trend: 'neutral',
                strength: 0,
                lastUpdate: Date.now()
            });
            
            this.metrics.totalSymbols++;
            this.logger.info(`🔔 Subscribed to ${symbol} via ${connectionId}`);
            
        } catch (error) {
            this.logger.error(`💥 Failed to subscribe to ${symbol}: ${error.message}`);
            this.metrics.errors++;
            throw error;
        }
    }
    
    detectSymbolCategory(symbol) {
        for (const [category, config] of Object.entries(this.symbolCategories)) {
            if (config.symbols.includes(symbol)) {
                return category;
            }
        }
        
        // Default to precious_metals if not found
        return 'precious_metals';
    }
    
    async handleMarketData(symbol, quoteData, connectionId, category) {
        try {
            const currentPrice = quoteData.lp || null;
            const connection = this.connections.get(connectionId);
            const characteristics = connection?.characteristics || {};
            
            // Get seasonal information
            const seasonalInfo = this.seasonalPatterns.get(symbol) || {};
            
            // Calculate commodity-specific metrics
            const volatility = Math.abs(quoteData.chp || 0);
            const isHighVolatility = this.isHighVolatilityForCategory(volatility, category);
            
            const marketData = {
                symbol,
                price: currentPrice,
                bid: quoteData.bid || null,
                ask: quoteData.ask || null,
                change: quoteData.ch || null,
                changePercent: quoteData.chp || null,
                volume: quoteData.volume || null,
                timestamp: new Date().toISOString(),
                source: 'commodities_collector',
                connectionId,
                category,
                commodityType: category,
                // Commodities-specific fields
                volatility,
                isHighVolatility,
                seasonalTrend: seasonalInfo.currentSeason || 'neutral',
                characteristics,
                supplyDemandTrend: this.getSupplyDemandTrend(symbol),
                inventoryStatus: this.getInventoryStatus(symbol),
                weatherRisk: this.getWeatherRisk(symbol, category),
                economicSensitivity: characteristics.economicSensitivity || 'medium'
            };
            
            // Seasonal alerts
            if (this.isSeasonalTransition(symbol)) {
                await this.sendSeasonalAlert(symbol, seasonalInfo, marketData);
                this.metrics.seasonalAlerts++;
            }
            
            // Supply/demand alerts
            const supplyDemandChange = this.detectSupplyDemandChange(symbol, currentPrice);
            if (supplyDemandChange) {
                await this.sendSupplyDemandAlert(symbol, supplyDemandChange, marketData);
                this.metrics.supplyDemandAlerts++;
            }
            
            // Weather-related alerts for agriculture
            if (category === 'agriculture' && this.hasWeatherRisk(symbol)) {
                await this.sendWeatherAlert(symbol, marketData);
                this.metrics.weatherAlerts++;
            }
            
            // Economic correlation updates
            await this.updateEconomicCorrelations(symbol, currentPrice, characteristics);
            
            // Publish to Redis
            if (this.redisManager) {
                await this.redisManager.publishMarketData(marketData);
            }
            
            // Emit event for local listeners
            this.emit('marketData', marketData);
            
            this.metrics.messagesPublished++;
            this.logger.debug(`📡 Published ${symbol}: ${currentPrice} (${seasonalInfo.currentSeason || 'neutral'} season)`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to handle market data for ${symbol}: ${error.message}`);
            this.metrics.errors++;
        }
    }
    
    isHighVolatilityForCategory(volatility, category) {
        const thresholds = {
            precious_metals: 3.0, // 3% for precious metals
            energy: 5.0, // 5% for energy (more volatile)
            base_metals: 4.0, // 4% for base metals
            agriculture: 6.0, // 6% for agriculture (very volatile)
            livestock: 4.0 // 4% for livestock
        };
        
        return volatility > (thresholds[category] || 3.0);
    }
    
    getSupplyDemandTrend(symbol) {
        const indicator = this.supplyDemandIndicators.get(symbol);
        return indicator ? indicator.trend : 'neutral';
    }
    
    getInventoryStatus(symbol) {
        // Placeholder for inventory status
        // In production, this would integrate with actual inventory data
        const statuses = ['low', 'normal', 'high', 'critical'];
        return statuses[Math.floor(Math.random() * statuses.length)];
    }
    
    getWeatherRisk(symbol, category) {
        if (category !== 'agriculture') return 'none';
        
        // Placeholder for weather risk assessment
        // In production, this would integrate with weather APIs
        const risks = ['none', 'low', 'medium', 'high', 'extreme'];
        return risks[Math.floor(Math.random() * risks.length)];
    }
    
    isSeasonalTransition(symbol) {
        const seasonal = this.seasonalPatterns.get(symbol);
        if (!seasonal) return false;
        
        const currentMonth = new Date().getMonth() + 1;
        const dayOfMonth = new Date().getDate();
        
        // Check if we're at the beginning of a strong or weak seasonal period
        return (seasonal.strongMonths.includes(currentMonth) || seasonal.weakMonths.includes(currentMonth)) && dayOfMonth <= 3;
    }
    
    detectSupplyDemandChange(symbol, currentPrice) {
        // Simplified supply/demand change detection
        // In production, this would use more sophisticated algorithms
        const indicator = this.supplyDemandIndicators.get(symbol);
        if (!indicator) return null;
        
        const priceChange = Math.random() * 10 - 5; // Placeholder
        
        if (Math.abs(priceChange) > 3) {
            const newTrend = priceChange > 0 ? 'bullish' : 'bearish';
            if (newTrend !== indicator.trend) {
                indicator.trend = newTrend;
                indicator.strength = Math.abs(priceChange);
                indicator.lastUpdate = Date.now();
                
                return {
                    oldTrend: indicator.trend,
                    newTrend,
                    strength: Math.abs(priceChange),
                    confidence: Math.min(100, Math.abs(priceChange) * 20)
                };
            }
        }
        
        return null;
    }
    
    hasWeatherRisk(symbol) {
        const weatherSensitive = ['WHEAT', 'CORN', 'SOYBEANS', 'COFFEE', 'SUGAR', 'COTTON'];
        return weatherSensitive.includes(symbol) && Math.random() > 0.8; // 20% chance
    }
    
    async sendSeasonalAlert(symbol, seasonalInfo, marketData) {
        try {
            const alert = {
                type: 'seasonal_transition',
                symbol,
                seasonalTrend: seasonalInfo.currentSeason,
                price: marketData.price,
                category: marketData.category,
                strongMonths: seasonalInfo.strongMonths,
                weakMonths: seasonalInfo.weakMonths,
                timestamp: marketData.timestamp,
                source: 'commodities_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('seasonal_transition', alert);
            }
            
            this.logger.info(`🌱 Seasonal alert: ${symbol} entering ${seasonalInfo.currentSeason} season`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send seasonal alert for ${symbol}: ${error.message}`);
        }
    }
    
    async sendSupplyDemandAlert(symbol, change, marketData) {
        try {
            const alert = {
                type: 'supply_demand_change',
                symbol,
                oldTrend: change.oldTrend,
                newTrend: change.newTrend,
                strength: change.strength,
                confidence: change.confidence,
                price: marketData.price,
                category: marketData.category,
                timestamp: marketData.timestamp,
                source: 'commodities_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('supply_demand_change', alert);
            }
            
            this.logger.info(`📊 Supply/Demand alert: ${symbol} trend changed to ${change.newTrend} (${change.confidence}% confidence)`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send supply/demand alert for ${symbol}: ${error.message}`);
        }
    }
    
    async sendWeatherAlert(symbol, marketData) {
        try {
            const alert = {
                type: 'weather_risk',
                symbol,
                weatherRisk: marketData.weatherRisk,
                price: marketData.price,
                category: marketData.category,
                timestamp: marketData.timestamp,
                source: 'commodities_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('weather_risk', alert);
            }
            
            this.logger.info(`🌦️ Weather alert: ${symbol} - ${marketData.weatherRisk} weather risk`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send weather alert for ${symbol}: ${error.message}`);
        }
    }
    
    async updateEconomicCorrelations(symbol, price, characteristics) {
        try {
            if (!characteristics.economicSensitivity || characteristics.economicSensitivity === 'low') {
                return;
            }
            
            // Placeholder for economic correlation updates
            // In production, this would correlate with economic indicators
            this.metrics.economicCorrelations++;
            
        } catch (error) {
            this.logger.error(`❌ Economic correlation update error for ${symbol}: ${error.message}`);
        }
    }
    
    startSeasonalAnalysis() {
        // Run seasonal analysis daily
        setInterval(() => {
            this.performSeasonalAnalysis();
        }, 86400000); // 24 hours
    }
    
    startSupplyDemandTracking() {
        // Update supply/demand indicators every hour
        setInterval(() => {
            this.updateSupplyDemandIndicators();
        }, 3600000); // 1 hour
    }
    
    async performSeasonalAnalysis() {
        try {
            const analysis = {
                timestamp: new Date().toISOString(),
                currentMonth: new Date().getMonth() + 1,
                seasonalTrends: {},
                recommendations: []
            };
            
            for (const [symbol, pattern] of this.seasonalPatterns.entries()) {
                const currentSeason = this.getCurrentSeason(pattern);
                analysis.seasonalTrends[symbol] = {
                    currentSeason,
                    strongMonths: pattern.strongMonths,
                    weakMonths: pattern.weakMonths
                };
                
                if (currentSeason === 'strong') {
                    analysis.recommendations.push(`${symbol}: Historically strong period - monitor for bullish momentum`);
                } else if (currentSeason === 'weak') {
                    analysis.recommendations.push(`${symbol}: Historically weak period - monitor for bearish pressure`);
                }
            }
            
            // Store analysis in Redis
            if (this.redisManager) {
                await this.redisManager.storeAnalysis('commodities_seasonal_analysis', analysis);
            }
            
            this.logger.info(`🌱 Seasonal analysis completed: ${analysis.recommendations.length} recommendations`);
            
        } catch (error) {
            this.logger.error(`❌ Seasonal analysis error: ${error.message}`);
        }
    }
    
    async updateSupplyDemandIndicators() {
        try {
            for (const [symbol, indicator] of this.supplyDemandIndicators.entries()) {
                // Placeholder for supply/demand updates
                // In production, this would integrate with actual supply/demand data
                indicator.lastUpdate = Date.now();
            }
            
            this.logger.debug(`📊 Updated supply/demand indicators for ${this.supplyDemandIndicators.size} commodities`);
            
        } catch (error) {
            this.logger.error(`❌ Supply/demand update error: ${error.message}`);
        }
    }
    
    async subscribeToAllCommodities() {
        try {
            this.logger.info('🥇 Subscribing to all commodities...');
            
            for (const [category, config] of Object.entries(this.symbolCategories)) {
                for (const symbol of config.symbols) {
                    await this.subscribeToSymbol(symbol, category);
                    
                    // Rate limiting delay
                    await new Promise(resolve => 
                        setTimeout(resolve, this.config.rateLimitDelay)
                    );
                }
            }
            
            this.logger.info(`✅ Subscribed to ${this.metrics.totalSymbols} commodities`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to subscribe to all commodities: ${error.message}`);
            throw error;
        }
    }
    
    getStatus() {
        return {
            isRunning: this.isRunning,
            metrics: this.metrics,
            connections: Object.fromEntries(
                Array.from(this.connections.entries()).map(([id, conn]) => [
                    id, 
                    {
                        category: conn.category,
                        symbolCount: conn.symbols.size,
                        errorCount: conn.errorCount,
                        lastActivity: conn.lastActivity,
                        characteristics: conn.characteristics
                    }
                ])
            ),
            health: Object.fromEntries(this.connectionHealth),
            subscriptions: this.subscriptions.size,
            seasonalPatterns: this.seasonalPatterns.size,
            supplyDemandTracking: this.supplyDemandIndicators.size
        };
    }
    
    async shutdown() {
        try {
            this.logger.info('🛑 Shutting down Commodities Collector...');
            
            this.isRunning = false;
            
            // Close all subscriptions
            for (const [symbol, subscription] of this.subscriptions.entries()) {
                try {
                    subscription.market.delete();
                    subscription.quoteSession.delete();
                } catch (error) {
                    this.logger.warn(`⚠️ Error closing subscription for ${symbol}: ${error.message}`);
                }
            }
            
            this.subscriptions.clear();
            this.connections.clear();
            this.seasonalPatterns.clear();
            this.supplyDemandIndicators.clear();
            this.economicCorrelations.clear();
            
            if (this.redisManager) {
                await this.redisManager.disconnect();
            }
            
            this.logger.info('✅ Commodities Collector shutdown complete');
            
        } catch (error) {
            this.logger.error(`❌ Shutdown error: ${error.message}`);
        }
    }
}

module.exports = CommoditiesCollector;

// If running directly
if (require.main === module) {
    const collector = new CommoditiesCollector({
        redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
        collectorId: process.env.COLLECTOR_ID || 'commodities_collector_1'
    });
    
    collector.initialize()
        .then(() => collector.subscribeToAllCommodities())
        .catch(error => {
            console.error('Failed to start Commodities Collector:', error);
            process.exit(1);
        });
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        collector.shutdown().then(() => process.exit(0));
    });
    
    process.on('SIGTERM', () => {
        collector.shutdown().then(() => process.exit(0));
    });
}
