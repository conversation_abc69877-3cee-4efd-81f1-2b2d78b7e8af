# Multi-stage Dockerfile for Specialized Collectors
# Optimized for Node.js applications with security best practices

# Build stage
FROM node:18-alpine as builder

# Set build arguments
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# Add metadata
LABEL maintainer="TradingView API Team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="tradingview-collector" \
      org.label-schema.description="Specialized collector for TradingView market data" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.schema-version="1.0"

# Set environment variables for build
ENV NODE_ENV=production \
    NPM_CONFIG_LOGLEVEL=warn \
    NPM_CONFIG_FUND=false \
    NPM_CONFIG_AUDIT=false

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Set work directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Production stage
FROM node:18-alpine as production

# Set production environment variables
ENV NODE_ENV=production \
    NODE_OPTIONS="--max-old-space-size=2048" \
    UV_THREADPOOL_SIZE=4

# Create non-root user for security
RUN addgroup -g 1001 -S appuser && \
    adduser -S appuser -u 1001 -G appuser

# Install runtime dependencies only
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tini

# Set work directory
WORKDIR /app

# Copy node_modules from builder stage
COPY --from=builder /app/node_modules ./node_modules

# Copy application code
COPY main.js ./
COPY src/ ./src/
COPY specialized_collectors/ ./specialized_collectors/

# Copy collector startup script
COPY <<EOF ./start-collector.js
#!/usr/bin/env node

const collectorType = process.env.COLLECTOR_TYPE || 'forex';
const collectorMap = {
    'forex': './specialized_collectors/forex_collector.js',
    'crypto': './specialized_collectors/crypto_collector.js',
    'indices': './specialized_collectors/indices_collector.js',
    'commodities': './specialized_collectors/commodities_collector.js'
};

const collectorPath = collectorMap[collectorType];
if (!collectorPath) {
    console.error(\`Unknown collector type: \${collectorType}\`);
    process.exit(1);
}

console.log(\`Starting \${collectorType} collector...\`);
require(collectorPath);
EOF

# Make startup script executable
RUN chmod +x ./start-collector.js

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/data && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Expose port
EXPOSE 3001

# Use tini as init system
ENTRYPOINT ["/sbin/tini", "--"]

# Default command
CMD ["node", "start-collector.js"]
