# System Architecture Details

## Core Components

### TradingView Client (`src/client.js`)
- **WebSocket connection management** to TradingView
- **Session-based architecture** (Quote/Chart sessions)  
- **Event-driven packet handling** and protocol parsing
- **Authentication support** for premium features
- Debug mode available with `DEBUG: true` option

### Protocol Handler (`src/protocol.js`)
- **WebSocket packet parsing** and formatting
- **Compressed data handling** with J<PERSON><PERSON>ip
- **Message serialization/deserialization**
- Handles TradingView's internal communication protocol

### Session Managers
**Quote Session (`src/quote/session.js`):**
- Real-time quote data streaming
- Market data subscription management
- Price, bid, ask updates

**Chart Session (`src/chart/session.js`):**
- Chart data and historical OHLCV
- Timeframe management
- Technical indicators support

### Indicator Classes
- `src/classes/BuiltInIndicator.js` - TradingView built-in indicators
- `src/classes/PineIndicator.js` - Custom Pine Script indicators
- `src/classes/PinePermManager.js` - Pine Script permissions management

## API Server Architecture

### FastAPI Server (`api_server.py`)
**Main Components:**
- **REST endpoints** for historical data, quotes, search
- **WebSocket endpoints** for real-time data streaming  
- **API key authentication middleware**
- **Swagger/OpenAPI documentation** at `/docs`
- **Connection manager** for WebSocket clients

**Key Classes:**
- `MarketDataRequest` - Pydantic model for market data requests
- `HistoricalDataRequest` - Historical data request validation
- `OrderRequest` - Trading order request model
- `ConnectionManager` - WebSocket connection management
- `NodeServiceClient` - Interface to Node.js service

### Node.js Services
**Basic Service (`node_service.js`):**
- Direct TradingView-API wrapper
- Basic error handling
- Simple HTTP API interface

**Robust Service (`node_service_robust.js`):**
- Enhanced error handling
- **Mock data fallback** when TradingView fails
- Better connection management
- Recommended for production use

## Data Flow

### Real-time Data
```
TradingView → WebSocket → Node.js Service → FastAPI → WebSocket → Client
```

### Historical Data  
```
TradingView → Node.js Service → FastAPI → REST API → Client
```

### Trading Operations (Future)
```
Client → FastAPI → Brokerage API → Trade Execution
```

## Environment Configuration

### Required Environment Variables
```bash
API_KEY_SECRET=your-secure-api-key
API_HOST=0.0.0.0
API_PORT=8000
NODE_SERVICE_HOST=localhost
NODE_SERVICE_PORT=3001
TRADINGVIEW_SESSION=optional-session-id
TRADINGVIEW_SIGNATURE=optional-signature
```

### Service Communication
- FastAPI communicates with Node.js service via HTTP
- Default Node.js service runs on port 3001
- FastAPI runs on port 8000
- WebSocket connections managed by FastAPI ConnectionManager