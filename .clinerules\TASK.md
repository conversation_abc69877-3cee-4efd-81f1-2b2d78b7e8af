# **TASK.md**

## **Current Tasks**

* \[ \] 1\. **Project Setup:**  
  * \[ \] 1.1. Initialize FastAPI project structure.  
  * \[ \] 1.2. Create requirements.txt with initial dependencies (FastAPI, uvicorn, websockets, httpx/requests).  
  * \[ \] 1.3. Implement basic server startup and shutdown logic.  
* \[ \] 2\. **Real-time Market Data Endpoint (WebSocket):**  
  * \[ \] 2.1. Design WebSocket endpoint /api/marketdata/realtime/{symbol}.  
  * \[ \] 2.2. Implement logic to connect to the unofficial Mathieu2301/TradingView-API for real-time data.  
  * \[ \] 2.3. Stream processed price, bid, and ask data to connected WebSocket clients.  
  * \[ \] 2.4. Add error handling for WebSocket connections and upstream data failures.  
* \[ \] 3\. **Historical Market Data Endpoint (REST):**  
  * \[ \] 3.1. Design REST GET endpoint /api/marketdata/historical/{symbol}/{timeframe}.  
  * \[ \] 3.2. Implement logic to fetch historical OHLCV data from the unofficial Mathieu2301/TradingView-API.  
  * \[ \] 3.3. Implement parameter validation for symbol, timeframe, start\_time, end\_time.  
* \[ \] 4\. **Trading Endpoints (REST):**  
  * \[ \] 4.1. Design REST POST endpoint /api/orders/place.  
  * \[ \] 4.2. Design REST DELETE endpoint /api/orders/cancel/{order\_id}.  
  * \[ \] 4.3. Design REST PUT endpoint /api/orders/modify/{order\_id}.  
  * \[ \] 4.4. Design REST POST endpoint /api/positions/close/{position\_id}.  
  * \[ \] 4.5. Implement placeholder logic for these trading actions (as the unofficial API might not support direct trading). *Note: This will likely require integration with an actual brokerage API in the future.*  
* \[ \] 5\. **Account & Portfolio Endpoints (REST):**  
  * \[ \] 5.1. Design REST GET endpoint /api/account/summary.  
  * \[ \] 5.2. Design REST GET endpoint /api/positions/open.  
  * \[ \] 5.3. Design REST GET endpoint /api/orders/pending.  
  * \[ \] 5.4. Design REST GET endpoint /api/history/trades.  
  * \[ \] 5.5. Implement placeholder logic for fetching this data (if the unofficial API provides it, otherwise mock for now).  
* \[ \] 6\. **Authentication & Authorization:**  
  * \[ \] 6.1. Implement a basic API key or token-based authentication mechanism for all endpoints.  
  * \[ \] 6.2. Add middleware for authentication and authorization.  
* \[ \] 7\. **Testing:**  
  * \[ \] 7.1. Write unit tests for the real-time data processing logic.  
  * \[ \] 7.2. Write unit tests for historical data fetching and parsing.  
  * \[ \] 7.3. Write unit tests for authentication/authorization.

## **Backlog / Discovered During Work**

* \[ \] Implement robust logging and monitoring.  
* \[ \] Add Dockerfile for containerization.  
* \[ \] Integrate with a proper, licensed market data provider (long-term).  
* \[ \] Integrate with a brokerage API for actual trade execution (long-term).  
* \[ \] Implement rate limiting on API endpoints.  
* \[ \] Improve data caching strategies.
