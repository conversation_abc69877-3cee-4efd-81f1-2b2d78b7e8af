/**
 * 📊 INDICES COLLECTOR - Enterprise WebSocket Service
 * 
 * Specialized collector for stock indices with optimized rate limiting
 * and connection management specifically for market indices.
 * 
 * Features:
 * - Dedicated to INDICES symbols only
 * - Optimized rate limiting for stock indices
 * - Session management for major global indices
 * - Market hours awareness and after-hours tracking
 * - Economic event correlation
 */

const TradingView = require('../main');
const { EnterpriseRedisManager } = require('./enterprise_redis_client');
const EventEmitter = require('events');

class IndicesCollector extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            maxConnections: 8, // Fewer connections needed for indices
            rateLimitDelay: 200, // 200ms between requests (indices-optimized)
            reconnectDelay: 5000,
            healthCheckInterval: 30000,
            redisUrl: config.redisUrl || 'redis://localhost:6379',
            collectorId: config.collectorId || 'indices_collector_1',
            marketHoursCheck: true, // Enable market hours tracking
            ...config
        };
        
        // Indices-specific symbol categories with market hours
        this.symbolCategories = {
            us_major: {
                symbols: ['SPX500', 'US30', 'NAS100', 'US2000'],
                timezone: 'America/New_York',
                marketOpen: '09:30',
                marketClose: '16:00',
                region: 'North America'
            },
            us_sector: {
                symbols: ['XLF', 'XLK', 'XLE', 'XLV', 'XLI', 'XLP'],
                timezone: 'America/New_York',
                marketOpen: '09:30',
                marketClose: '16:00',
                region: 'North America'
            },
            europe: {
                symbols: ['UK100', 'GER40', 'FRA40', 'ESP35', 'ITA40', 'EU50'],
                timezone: 'Europe/London',
                marketOpen: '08:00',
                marketClose: '16:30',
                region: 'Europe'
            },
            asia_pacific: {
                symbols: ['JPN225', 'HK50', 'AUS200', 'SING30', 'IND50'],
                timezone: 'Asia/Tokyo',
                marketOpen: '09:00',
                marketClose: '15:00',
                region: 'Asia Pacific'
            },
            emerging: {
                symbols: ['BRA50', 'MEX35', 'ZAF40', 'RUS50', 'CHN50'],
                timezone: 'UTC',
                marketOpen: '09:00',
                marketClose: '17:00',
                region: 'Emerging Markets'
            }
        };
        
        // Connection management
        this.connections = new Map();
        this.subscriptions = new Map();
        this.connectionHealth = new Map();
        this.isRunning = false;
        
        // Indices-specific features
        this.marketHours = new Map(); // Track market hours for each region
        this.correlationMatrix = new Map(); // Track index correlations
        this.volatilityIndex = new Map(); // Track VIX-like volatility
        this.sectorRotation = new Map(); // Track sector rotation patterns
        
        // Rate limiting
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.lastRequestTime = 0;
        
        // Redis integration
        this.redisManager = null;
        
        // Performance metrics
        this.metrics = {
            totalSymbols: 0,
            activeConnections: 0,
            messagesPublished: 0,
            marketHoursAlerts: 0,
            correlationUpdates: 0,
            volatilityAlerts: 0,
            errors: 0,
            reconnections: 0,
            startTime: Date.now()
        };
        
        this.logger = {
            info: (msg) => console.log(`[INDICES-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            error: (msg) => console.error(`[INDICES-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            warn: (msg) => console.warn(`[INDICES-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            debug: (msg) => console.log(`[INDICES-COLLECTOR-DEBUG] ${new Date().toISOString()} - ${msg}`)
        };
    }
    
    async initialize() {
        try {
            this.logger.info('🚀 Initializing Indices Collector...');
            
            // Initialize Redis connection
            this.redisManager = new EnterpriseRedisManager({
                redisUrl: this.config.redisUrl,
                collectorId: this.config.collectorId
            });
            
            await this.redisManager.initialize();
            
            // Initialize market hours tracking
            this.initializeMarketHours();
            
            // Create initial connections for different index categories
            await this.createCategoryConnections();
            
            // Start background tasks
            this.startHealthMonitoring();
            this.startMetricsReporting();
            this.startMarketHoursMonitoring();
            this.startCorrelationAnalysis();
            
            this.isRunning = true;
            this.logger.info('✅ Indices Collector initialized successfully');
            
            return true;
            
        } catch (error) {
            this.logger.error(`❌ Failed to initialize Indices Collector: ${error.message}`);
            throw error;
        }
    }
    
    initializeMarketHours() {
        for (const [category, config] of Object.entries(this.symbolCategories)) {
            this.marketHours.set(category, {
                ...config,
                isOpen: false,
                nextOpen: null,
                nextClose: null,
                lastUpdate: Date.now()
            });
        }
    }
    
    async createCategoryConnections() {
        const categories = Object.keys(this.symbolCategories);
        
        for (const category of categories) {
            try {
                const connectionId = `indices_${category}`;
                const client = this.createTradingViewClient();
                
                this.connections.set(connectionId, {
                    client,
                    category,
                    symbols: new Set(),
                    lastActivity: Date.now(),
                    errorCount: 0,
                    marketConfig: this.symbolCategories[category]
                });
                
                this.connectionHealth.set(connectionId, {
                    status: 'healthy',
                    lastCheck: Date.now(),
                    successRate: 100,
                    avgResponseTime: 0,
                    category,
                    region: this.symbolCategories[category].region
                });
                
                this.logger.info(`🔗 Created connection for ${category} indices: ${connectionId}`);
                
            } catch (error) {
                this.logger.error(`❌ Failed to create connection for ${category}: ${error.message}`);
            }
        }
        
        this.metrics.activeConnections = this.connections.size;
    }
    
    createTradingViewClient() {
        const session = process.env.TRADINGVIEW_SESSION;
        const signature = process.env.TRADINGVIEW_SIGNATURE;
        
        if (session && signature) {
            return new TradingView.Client({
                token: session,
                signature: signature
            });
        } else {
            return new TradingView.Client();
        }
    }
    
    async subscribeToSymbol(symbol, category = null) {
        try {
            // Auto-detect category if not provided
            if (!category) {
                category = this.detectSymbolCategory(symbol);
            }
            
            const connectionId = `indices_${category}`;
            const connection = this.connections.get(connectionId);
            
            if (!connection) {
                throw new Error(`No connection available for category: ${category}`);
            }
            
            // Check if already subscribed
            if (this.subscriptions.has(symbol)) {
                this.logger.warn(`⚠️ Already subscribed to ${symbol}`);
                return;
            }
            
            const client = connection.client;
            const quoteSession = new client.Session.Quote({ fields: 'all' });
            const market = new quoteSession.Market(symbol);
            
            // Set up real-time data handler
            market.onData((quoteData) => {
                this.handleMarketData(symbol, quoteData, connectionId, category);
            });
            
            market.onLoaded(() => {
                this.logger.info(`✅ Market loaded: ${symbol} (${category})`);
            });
            
            market.onError((...errorArgs) => {
                const errorMessage = errorArgs.join(' ');
                this.logger.error(`❌ Market error for ${symbol}: ${errorMessage}`);
                this.handleConnectionError(connectionId, symbol, errorMessage);
            });
            
            // Track subscription
            this.subscriptions.set(symbol, {
                market,
                quoteSession,
                connectionId,
                category,
                subscribedAt: Date.now(),
                marketConfig: connection.marketConfig
            });
            
            connection.symbols.add(symbol);
            connection.lastActivity = Date.now();
            
            // Initialize correlation tracking
            this.correlationMatrix.set(symbol, new Map());
            
            this.metrics.totalSymbols++;
            this.logger.info(`🔔 Subscribed to ${symbol} via ${connectionId}`);
            
        } catch (error) {
            this.logger.error(`💥 Failed to subscribe to ${symbol}: ${error.message}`);
            this.metrics.errors++;
            throw error;
        }
    }
    
    detectSymbolCategory(symbol) {
        for (const [category, config] of Object.entries(this.symbolCategories)) {
            if (config.symbols.includes(symbol)) {
                return category;
            }
        }
        
        // Default to us_major if not found
        return 'us_major';
    }
    
    async handleMarketData(symbol, quoteData, connectionId, category) {
        try {
            const currentPrice = quoteData.lp || null;
            const connection = this.connections.get(connectionId);
            const marketConfig = connection?.marketConfig || {};
            
            // Check market hours
            const marketStatus = this.getMarketStatus(category);
            
            const marketData = {
                symbol,
                price: currentPrice,
                bid: quoteData.bid || null,
                ask: quoteData.ask || null,
                change: quoteData.ch || null,
                changePercent: quoteData.chp || null,
                volume: quoteData.volume || null,
                timestamp: new Date().toISOString(),
                source: 'indices_collector',
                connectionId,
                category,
                region: marketConfig.region || 'Unknown',
                // Indices-specific fields
                marketStatus: marketStatus.status,
                isMarketHours: marketStatus.isOpen,
                nextMarketEvent: marketStatus.nextEvent,
                volatility: Math.abs(quoteData.chp || 0),
                isHighVolatility: Math.abs(quoteData.chp || 0) > 2.0, // 2% threshold for indices
                marketCap: this.estimateMarketCap(symbol, currentPrice)
            };
            
            // Market hours alerts
            if (marketStatus.justOpened || marketStatus.justClosed) {
                await this.sendMarketHoursAlert(symbol, marketStatus, marketData);
                this.metrics.marketHoursAlerts++;
            }
            
            // Volatility alerts for indices
            if (marketData.isHighVolatility) {
                await this.sendVolatilityAlert(symbol, marketData);
                this.metrics.volatilityAlerts++;
            }
            
            // Update correlation matrix
            await this.updateCorrelations(symbol, currentPrice, category);
            
            // Publish to Redis
            if (this.redisManager) {
                await this.redisManager.publishMarketData(marketData);
            }
            
            // Emit event for local listeners
            this.emit('marketData', marketData);
            
            this.metrics.messagesPublished++;
            this.logger.debug(`📡 Published ${symbol}: ${currentPrice} (${marketStatus.status})`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to handle market data for ${symbol}: ${error.message}`);
            this.metrics.errors++;
        }
    }
    
    getMarketStatus(category) {
        const marketHours = this.marketHours.get(category);
        if (!marketHours) {
            return { status: 'unknown', isOpen: false, nextEvent: null };
        }
        
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentTime = currentHour * 60 + currentMinute;
        
        // Parse market hours (simplified - in production use proper timezone handling)
        const [openHour, openMinute] = marketHours.marketOpen.split(':').map(Number);
        const [closeHour, closeMinute] = marketHours.marketClose.split(':').map(Number);
        const openTime = openHour * 60 + openMinute;
        const closeTime = closeHour * 60 + closeMinute;
        
        const isOpen = currentTime >= openTime && currentTime <= closeTime;
        const wasOpen = marketHours.isOpen;
        
        // Detect market open/close events
        const justOpened = !wasOpen && isOpen;
        const justClosed = wasOpen && !isOpen;
        
        // Update market hours state
        marketHours.isOpen = isOpen;
        marketHours.lastUpdate = Date.now();
        
        let status = 'closed';
        let nextEvent = null;
        
        if (isOpen) {
            status = 'open';
            nextEvent = `Market closes at ${marketHours.marketClose}`;
        } else if (currentTime < openTime) {
            status = 'pre_market';
            nextEvent = `Market opens at ${marketHours.marketOpen}`;
        } else {
            status = 'after_hours';
            nextEvent = `Market opens tomorrow at ${marketHours.marketOpen}`;
        }
        
        return {
            status,
            isOpen,
            nextEvent,
            justOpened,
            justClosed,
            timezone: marketHours.timezone
        };
    }
    
    estimateMarketCap(symbol, price) {
        // Simplified market cap estimation based on symbol
        const marketCapEstimates = {
            'SPX500': 'Large Cap',
            'US30': 'Large Cap',
            'NAS100': 'Large Cap',
            'US2000': 'Small Cap',
            'UK100': 'Large Cap',
            'GER40': 'Large Cap',
            'JPN225': 'Large Cap'
        };
        
        return marketCapEstimates[symbol] || 'Mixed Cap';
    }
    
    async sendMarketHoursAlert(symbol, marketStatus, marketData) {
        try {
            const alert = {
                type: 'market_hours',
                symbol,
                event: marketStatus.justOpened ? 'market_open' : 'market_close',
                marketStatus: marketStatus.status,
                price: marketData.price,
                region: marketData.region,
                timezone: marketStatus.timezone,
                timestamp: marketData.timestamp,
                source: 'indices_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('market_hours', alert);
            }
            
            const eventText = marketStatus.justOpened ? 'opened' : 'closed';
            this.logger.info(`🕐 Market hours alert: ${symbol} market ${eventText} at ${marketData.price}`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send market hours alert for ${symbol}: ${error.message}`);
        }
    }
    
    async sendVolatilityAlert(symbol, marketData) {
        try {
            const alert = {
                type: 'high_volatility',
                symbol,
                price: marketData.price,
                change: marketData.change,
                changePercent: marketData.changePercent,
                volatility: marketData.volatility,
                category: marketData.category,
                region: marketData.region,
                marketStatus: marketData.marketStatus,
                timestamp: marketData.timestamp,
                source: 'indices_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('high_volatility', alert);
            }
            
            this.logger.info(`⚡ Volatility alert: ${symbol} - ${marketData.changePercent}% change`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send volatility alert for ${symbol}: ${error.message}`);
        }
    }
    
    async updateCorrelations(symbol, price, category) {
        try {
            // Simple correlation tracking (in production, use more sophisticated algorithms)
            const correlations = this.correlationMatrix.get(symbol);
            if (!correlations) return;
            
            // Update correlations with other symbols in the same category
            const categoryConfig = this.symbolCategories[category];
            if (!categoryConfig) return;
            
            for (const otherSymbol of categoryConfig.symbols) {
                if (otherSymbol === symbol) continue;
                
                const otherSubscription = this.subscriptions.get(otherSymbol);
                if (!otherSubscription) continue;
                
                // Simple correlation calculation (placeholder)
                const correlation = this.calculateSimpleCorrelation(symbol, otherSymbol);
                correlations.set(otherSymbol, correlation);
            }
            
            this.metrics.correlationUpdates++;
            
        } catch (error) {
            this.logger.error(`❌ Correlation update error for ${symbol}: ${error.message}`);
        }
    }
    
    calculateSimpleCorrelation(symbol1, symbol2) {
        // Placeholder for correlation calculation
        // In production, this would use historical price data and proper statistical methods
        return Math.random() * 2 - 1; // Random correlation between -1 and 1
    }
    
    startMarketHoursMonitoring() {
        // Check market hours every minute
        setInterval(() => {
            this.updateAllMarketHours();
        }, 60000);
    }
    
    updateAllMarketHours() {
        for (const category of Object.keys(this.symbolCategories)) {
            this.getMarketStatus(category); // This updates the market hours state
        }
    }
    
    startCorrelationAnalysis() {
        // Run correlation analysis every 5 minutes
        setInterval(() => {
            this.performCorrelationAnalysis();
        }, 300000);
    }
    
    async performCorrelationAnalysis() {
        try {
            const analysis = {
                timestamp: new Date().toISOString(),
                categories: {},
                strongCorrelations: [],
                weakCorrelations: []
            };
            
            for (const [category, config] of Object.entries(this.symbolCategories)) {
                analysis.categories[category] = {
                    region: config.region,
                    symbolCount: config.symbols.length,
                    activeSymbols: config.symbols.filter(s => this.subscriptions.has(s)).length
                };
            }
            
            // Store analysis in Redis
            if (this.redisManager) {
                await this.redisManager.storeAnalysis('indices_correlation_analysis', analysis);
            }
            
            this.logger.debug(`📊 Correlation analysis completed for ${Object.keys(analysis.categories).length} categories`);
            
        } catch (error) {
            this.logger.error(`❌ Correlation analysis error: ${error.message}`);
        }
    }
    
    async subscribeToAllIndices() {
        try {
            this.logger.info('📊 Subscribing to all indices...');
            
            for (const [category, config] of Object.entries(this.symbolCategories)) {
                for (const symbol of config.symbols) {
                    await this.subscribeToSymbol(symbol, category);
                    
                    // Rate limiting delay
                    await new Promise(resolve => 
                        setTimeout(resolve, this.config.rateLimitDelay)
                    );
                }
            }
            
            this.logger.info(`✅ Subscribed to ${this.metrics.totalSymbols} indices`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to subscribe to all indices: ${error.message}`);
            throw error;
        }
    }
    
    getStatus() {
        return {
            isRunning: this.isRunning,
            metrics: this.metrics,
            connections: Object.fromEntries(
                Array.from(this.connections.entries()).map(([id, conn]) => [
                    id, 
                    {
                        category: conn.category,
                        symbolCount: conn.symbols.size,
                        errorCount: conn.errorCount,
                        lastActivity: conn.lastActivity,
                        region: conn.marketConfig?.region
                    }
                ])
            ),
            health: Object.fromEntries(this.connectionHealth),
            subscriptions: this.subscriptions.size,
            marketHours: Object.fromEntries(this.marketHours),
            correlationTracking: this.correlationMatrix.size
        };
    }
    
    async shutdown() {
        try {
            this.logger.info('🛑 Shutting down Indices Collector...');
            
            this.isRunning = false;
            
            // Close all subscriptions
            for (const [symbol, subscription] of this.subscriptions.entries()) {
                try {
                    subscription.market.delete();
                    subscription.quoteSession.delete();
                } catch (error) {
                    this.logger.warn(`⚠️ Error closing subscription for ${symbol}: ${error.message}`);
                }
            }
            
            this.subscriptions.clear();
            this.connections.clear();
            this.correlationMatrix.clear();
            this.marketHours.clear();
            
            if (this.redisManager) {
                await this.redisManager.disconnect();
            }
            
            this.logger.info('✅ Indices Collector shutdown complete');
            
        } catch (error) {
            this.logger.error(`❌ Shutdown error: ${error.message}`);
        }
    }
}

module.exports = IndicesCollector;

// If running directly
if (require.main === module) {
    const collector = new IndicesCollector({
        redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
        collectorId: process.env.COLLECTOR_ID || 'indices_collector_1'
    });
    
    collector.initialize()
        .then(() => collector.subscribeToAllIndices())
        .catch(error => {
            console.error('Failed to start Indices Collector:', error);
            process.exit(1);
        });
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        collector.shutdown().then(() => process.exit(0));
    });
    
    process.on('SIGTERM', () => {
        collector.shutdown().then(() => process.exit(0));
    });
}
