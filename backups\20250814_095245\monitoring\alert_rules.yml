# Prometheus Alert Rules for TradingView API Enterprise
# Financial trading system specific alerts with appropriate thresholds

groups:
  # High-priority system alerts
  - name: system.critical
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 30s
        labels:
          severity: critical
          category: availability
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 30 seconds"
          runbook_url: "https://runbooks.tradingview-api.com/service-down"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
          category: errors
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }} on {{ $labels.instance }}"

      - alert: APIResponseTimeHigh
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "API response time is high"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

  # Market data specific alerts
  - name: market_data.critical
    rules:
      - alert: MarketDataStale
        expr: time() - market_data_last_update_timestamp > 60
        for: 1m
        labels:
          severity: critical
          category: data_quality
        annotations:
          summary: "Market data is stale for {{ $labels.symbol }}"
          description: "No market data updates for {{ $labels.symbol }} in the last {{ $value }} seconds"

      - alert: CollectorConnectionLoss
        expr: collector_active_connections < 1
        for: 30s
        labels:
          severity: critical
          category: connectivity
        annotations:
          summary: "{{ $labels.collector_type }} collector has no active connections"
          description: "Collector {{ $labels.collector_type }} has lost all TradingView connections"

      - alert: HighVolumeSpike
        expr: increase(volume_spike_alerts_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          category: market_activity
        annotations:
          summary: "High number of volume spikes detected"
          description: "{{ $value }} volume spikes detected in the last 5 minutes"

      - alert: PriceDataGap
        expr: time() - price_data_last_timestamp > 300
        for: 1m
        labels:
          severity: warning
          category: data_quality
        annotations:
          summary: "Price data gap detected for {{ $labels.symbol }}"
          description: "No price updates for {{ $labels.symbol }} in {{ $value }} seconds"

  # Redis and caching alerts
  - name: redis.critical
    rules:
      - alert: RedisDown
        expr: redis_up == 0
        for: 30s
        labels:
          severity: critical
          category: infrastructure
        annotations:
          summary: "Redis instance is down"
          description: "Redis instance {{ $labels.instance }} is not responding"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: RedisConnectionsHigh
        expr: redis_connected_clients > 1000
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "High number of Redis connections"
          description: "{{ $value }} clients connected to Redis on {{ $labels.instance }}"

  # WebSocket and real-time alerts
  - name: websocket.critical
    rules:
      - alert: WebSocketConnectionsLow
        expr: websocket_active_connections < 1
        for: 2m
        labels:
          severity: warning
          category: connectivity
        annotations:
          summary: "Low WebSocket connections"
          description: "Only {{ $value }} active WebSocket connections on {{ $labels.instance }}"

      - alert: WebSocketMessageBacklog
        expr: websocket_message_queue_size > 1000
        for: 1m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "WebSocket message backlog detected"
          description: "{{ $value }} messages queued for WebSocket delivery"

  # Webhook delivery alerts
  - name: webhooks.critical
    rules:
      - alert: WebhookDeliveryFailureRate
        expr: rate(webhook_delivery_failures_total[5m]) / rate(webhook_delivery_attempts_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          category: integrations
        annotations:
          summary: "High webhook delivery failure rate"
          description: "Webhook failure rate is {{ $value | humanizePercentage }} for endpoint {{ $labels.endpoint_id }}"

      - alert: WebhookDeadLetterQueue
        expr: webhook_dead_letter_queue_size > 100
        for: 5m
        labels:
          severity: warning
          category: integrations
        annotations:
          summary: "Webhook dead letter queue is growing"
          description: "{{ $value }} webhooks in dead letter queue"

      - alert: WebhookCircuitBreakerOpen
        expr: webhook_circuit_breaker_state == 1
        for: 1m
        labels:
          severity: critical
          category: integrations
        annotations:
          summary: "Webhook circuit breaker is open"
          description: "Circuit breaker is open for webhook endpoint {{ $labels.endpoint_id }}"

  # Resource utilization alerts
  - name: resources.warning
    rules:
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / (1024*1024*1024) > 2
        for: 10m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}GB on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} full on {{ $labels.instance }}"

  # Business logic alerts
  - name: business.warning
    rules:
      - alert: TradingHoursDataGap
        expr: (time() % 86400) > 32400 and (time() % 86400) < 72000 and rate(market_data_messages_total[5m]) < 10
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "Low market data activity during trading hours"
          description: "Only {{ $value }} market data messages per second during trading hours"

      - alert: UnusualVolumePattern
        expr: avg_over_time(market_volume[1h]) > 5 * avg_over_time(market_volume[24h])
        for: 10m
        labels:
          severity: info
          category: market_activity
        annotations:
          summary: "Unusual volume pattern detected"
          description: "Current volume is {{ $value }}x higher than 24h average for {{ $labels.symbol }}"

      - alert: PriceVolatilityHigh
        expr: abs(rate(market_price[5m])) > 0.05
        for: 2m
        labels:
          severity: info
          category: market_activity
        annotations:
          summary: "High price volatility detected"
          description: "Price volatility is {{ $value | humanizePercentage }} for {{ $labels.symbol }}"

  # Security alerts
  - name: security.critical
    rules:
      - alert: UnauthorizedAccess
        expr: rate(http_requests_total{status="401"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High number of unauthorized requests"
          description: "{{ $value }} unauthorized requests per second from {{ $labels.instance }}"

      - alert: RateLimitExceeded
        expr: rate(http_requests_total{status="429"}[5m]) > 5
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Rate limit frequently exceeded"
          description: "{{ $value }} rate limit violations per second"

      - alert: SuspiciousTrafficPattern
        expr: rate(http_requests_total[1m]) > 1000
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Suspicious traffic pattern detected"
          description: "{{ $value }} requests per second, which is unusually high"
