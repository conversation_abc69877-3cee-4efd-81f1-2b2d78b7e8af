# **Prop Firm API Endpoint Server**

An API server designed to provide real-time market data and facilitate trading operations for a proprietary trading firm. This server acts as a crucial intermediary, abstracting the complexities of data acquisition and trade execution.


* **Instability:** The unofficial API may break without warning if TradingView changes its internal systems.  
* **Terms of Service Violation:** Use of unofficial APIs may violate TradingView's terms of service, potentially leading to IP blocks or legal action.  
* **Scalability & Latency:** Performance and reliability for high-frequency trading cannot be guaranteed.  
* **Security:** As an unofficial solution, security vulnerabilities may exist.

**For a production proprietary trading environment, it is strongly recommended to transition to official, licensed market data providers and brokerage APIs as soon as possible.**

## **Features**

* **Real-time Market Data Streaming:** Provides live price, bid, and ask data via WebSocket.  
* **Historical Market Data:** Fetches OHLCV data for charting.  
* **Trading Operations:** Endpoints for placing, canceling, and modifying orders, and closing positions.  
* **Account & Portfolio Management:** Retrieves account summary, open positions, pending orders, and trade history.  
* **Authentication:** Basic API key/token authentication for secure access.

## **Getting Started**

### **Prerequisites**

* Python 3.8+  
* pip (Python package installer)

### **Installation**

1. **Clone the repository:**  
   git clone \<your-repo-url\>  
   cd prop-firm-api-server

2. **Create a virtual environment (recommended):**  
   python \-m venv venv  
   source venv/bin/activate  \# On Windows: \`venv\\Scripts\\activate\`

3. **Install dependencies:**  
   pip install \-r requirements.txt

### **Configuration**

Create a .env file in the root directory for sensitive information.

\# Example .env file  
UNOFFICIAL\_TV\_API\_BASE\_URL="\<base-url-of-unofficial-tradingview-api\>"  
API\_KEY\_SECRET="your\_secure\_api\_key\_secret"

*Replace \<base-url-of-unofficial-tradingview-api\> with the actual base URL of the Mathieu2301/TradingView-API server you are using or hosting.*

### **Running the Server**

To run the FastAPI server using Uvicorn:

uvicorn main:app \--reload \--host 0.0.0.0 \--port 8000

The API will be accessible at http://localhost:8000.

## **API Endpoints**

| Category | Endpoint | Method | Description |
| :---- | :---- | :---- | :---- |
| **Market Data** | /api/marketdata/realtime/{symbol} | WS | Streams real-time price, bid, and ask data. |
| **Market Data** | /api/marketdata/historical/{symbol}/{timeframe} | GET | Retrieves historical OHLCV data for charting. |
| **Trading Operations** | /api/orders/place | POST | Places a new market, limit, or stop order. |
| **Trading Operations** | /api/orders/cancel/{order\_id} | DELETE | Cancels a pending order. |
| **Trading Operations** | /api/orders/modify/{order\_id} | PUT | Modifies an existing pending order. |
| **Trading Operations** | /api/positions/close/{position\_id} | POST | Closes an open trading position. |
| **Account & Portfolio** | /api/account/summary | GET | Fetches overall account balance, equity, and margin details. |
| **Account & Portfolio** | /api/positions/open | GET | Retrieves all currently open trading positions. |
| **Account & Portfolio** | /api/orders/pending | GET | Retrieves all pending (working) orders. |
| **Account & Portfolio** | /api/history/trades | GET | Retrieves a history of all closed trades. |

Detailed request/response schemas will be available via the FastAPI interactive documentation (/docs endpoint when the server is running).

## **Development**

### **Testing**

Unit tests are located in the tests/ directory. To run tests:

pytest

### **Contributing**

Contributions are welcome, especially for improving reliability, performance, and transitioning to official data sources.