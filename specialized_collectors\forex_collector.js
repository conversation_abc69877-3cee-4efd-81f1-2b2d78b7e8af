/**
 * 🌍 FOREX COLLECTOR - Enterprise WebSocket Service
 * 
 * Specialized collector for Forex symbols with optimized rate limiting
 * and connection management specifically for currency pairs.
 * 
 * Features:
 * - Dedicated to FOREX symbols only
 * - Optimized rate limiting for currency pairs
 * - Session management for major/minor/exotic pairs
 * - Redis integration for enterprise data flow
 */

const TradingView = require('../main');
const { EnterpriseRedisManager } = require('./enterprise_redis_client');
const EventEmitter = require('events');

class ForexCollector extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            maxConnections: 10,
            rateLimitDelay: 150, // 150ms between requests (forex-optimized)
            reconnectDelay: 5000,
            healthCheckInterval: 30000,
            redisUrl: config.redisUrl || 'redis://localhost:6379',
            collectorId: config.collectorId || 'forex_collector_1',
            ...config
        };
        
        // Forex-specific symbol categories
        this.symbolCategories = {
            major: ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'],
            minor: ['EURJPY', 'GBPJPY', 'EURGBP', 'AUDCAD', 'GBPAUD', 'EURAUD'],
            exotic: ['USDTRY', 'USDZAR', 'USDMXN', 'USDSEK', 'USDNOK', 'USDDKK']
        };
        
        // Connection management
        this.connections = new Map();
        this.subscriptions = new Map();
        this.connectionHealth = new Map();
        this.isRunning = false;
        
        // Rate limiting
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.lastRequestTime = 0;
        
        // Redis integration
        this.redisManager = null;
        
        // Performance metrics
        this.metrics = {
            totalSymbols: 0,
            activeConnections: 0,
            messagesPublished: 0,
            errors: 0,
            reconnections: 0,
            startTime: Date.now()
        };
        
        this.logger = {
            info: (msg) => console.log(`[FOREX-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            error: (msg) => console.error(`[FOREX-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            warn: (msg) => console.warn(`[FOREX-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            debug: (msg) => console.log(`[FOREX-COLLECTOR-DEBUG] ${new Date().toISOString()} - ${msg}`)
        };
    }
    
    async initialize() {
        try {
            this.logger.info('🚀 Initializing Forex Collector...');
            
            // Initialize Redis connection
            this.redisManager = new EnterpriseRedisManager({
                redisUrl: this.config.redisUrl,
                collectorId: this.config.collectorId
            });
            
            await this.redisManager.initialize();
            
            // Create initial connections for different forex categories
            await this.createCategoryConnections();
            
            // Start background tasks
            this.startHealthMonitoring();
            this.startMetricsReporting();
            
            this.isRunning = true;
            this.logger.info('✅ Forex Collector initialized successfully');
            
            return true;
            
        } catch (error) {
            this.logger.error(`❌ Failed to initialize Forex Collector: ${error.message}`);
            throw error;
        }
    }
    
    async createCategoryConnections() {
        const categories = Object.keys(this.symbolCategories);
        
        for (const category of categories) {
            try {
                const connectionId = `forex_${category}`;
                const client = this.createTradingViewClient();
                
                this.connections.set(connectionId, {
                    client,
                    category,
                    symbols: new Set(),
                    lastActivity: Date.now(),
                    errorCount: 0
                });
                
                this.connectionHealth.set(connectionId, {
                    status: 'healthy',
                    lastCheck: Date.now(),
                    successRate: 100,
                    avgResponseTime: 0
                });
                
                this.logger.info(`🔗 Created connection for ${category} forex pairs: ${connectionId}`);
                
            } catch (error) {
                this.logger.error(`❌ Failed to create connection for ${category}: ${error.message}`);
            }
        }
        
        this.metrics.activeConnections = this.connections.size;
    }
    
    createTradingViewClient() {
        const session = process.env.TRADINGVIEW_SESSION;
        const signature = process.env.TRADINGVIEW_SIGNATURE;
        
        if (session && signature) {
            return new TradingView.Client({
                token: session,
                signature: signature
            });
        } else {
            return new TradingView.Client();
        }
    }
    
    async subscribeToSymbol(symbol, category = null) {
        try {
            // Auto-detect category if not provided
            if (!category) {
                category = this.detectSymbolCategory(symbol);
            }
            
            const connectionId = `forex_${category}`;
            const connection = this.connections.get(connectionId);
            
            if (!connection) {
                throw new Error(`No connection available for category: ${category}`);
            }
            
            // Check if already subscribed
            if (this.subscriptions.has(symbol)) {
                this.logger.warn(`⚠️ Already subscribed to ${symbol}`);
                return;
            }
            
            const client = connection.client;
            const quoteSession = new client.Session.Quote({ fields: 'all' });
            const market = new quoteSession.Market(symbol);
            
            // Set up real-time data handler
            market.onData((quoteData) => {
                this.handleMarketData(symbol, quoteData, connectionId);
            });
            
            market.onLoaded(() => {
                this.logger.info(`✅ Market loaded: ${symbol} (${category})`);
            });
            
            market.onError((...errorArgs) => {
                const errorMessage = errorArgs.join(' ');
                this.logger.error(`❌ Market error for ${symbol}: ${errorMessage}`);
                this.handleConnectionError(connectionId, symbol, errorMessage);
            });
            
            // Track subscription
            this.subscriptions.set(symbol, {
                market,
                quoteSession,
                connectionId,
                category,
                subscribedAt: Date.now()
            });
            
            connection.symbols.add(symbol);
            connection.lastActivity = Date.now();
            
            this.metrics.totalSymbols++;
            this.logger.info(`🔔 Subscribed to ${symbol} via ${connectionId}`);
            
        } catch (error) {
            this.logger.error(`💥 Failed to subscribe to ${symbol}: ${error.message}`);
            this.metrics.errors++;
            throw error;
        }
    }
    
    detectSymbolCategory(symbol) {
        for (const [category, symbols] of Object.entries(this.symbolCategories)) {
            if (symbols.includes(symbol)) {
                return category;
            }
        }
        
        // Default to major if not found
        return 'major';
    }
    
    async handleMarketData(symbol, quoteData, connectionId) {
        try {
            const marketData = {
                symbol,
                price: quoteData.lp || null,
                bid: quoteData.bid || null,
                ask: quoteData.ask || null,
                spread: quoteData.ask && quoteData.bid ? (quoteData.ask - quoteData.bid) : null,
                change: quoteData.ch || null,
                changePercent: quoteData.chp || null,
                volume: quoteData.volume || null,
                timestamp: new Date().toISOString(),
                source: 'forex_collector',
                connectionId,
                category: this.connections.get(connectionId)?.category || 'unknown'
            };
            
            // Publish to Redis
            if (this.redisManager) {
                await this.redisManager.publishMarketData(marketData);
            }
            
            // Emit event for local listeners
            this.emit('marketData', marketData);
            
            this.metrics.messagesPublished++;
            this.logger.debug(`📡 Published ${symbol}: ${marketData.price}`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to handle market data for ${symbol}: ${error.message}`);
            this.metrics.errors++;
        }
    }
    
    async handleConnectionError(connectionId, symbol, errorMessage) {
        try {
            const connection = this.connections.get(connectionId);
            if (connection) {
                connection.errorCount++;
                connection.lastActivity = Date.now();
            }
            
            const health = this.connectionHealth.get(connectionId);
            if (health) {
                health.status = 'degraded';
                health.lastCheck = Date.now();
            }
            
            this.logger.warn(`🔄 Handling connection error for ${connectionId}: ${errorMessage}`);
            
            // Attempt to reconnect if too many errors
            if (connection && connection.errorCount > 5) {
                await this.reconnectConnection(connectionId);
            }
            
        } catch (error) {
            this.logger.error(`💥 Error handling connection error: ${error.message}`);
        }
    }
    
    async reconnectConnection(connectionId) {
        try {
            this.logger.info(`🔄 Reconnecting ${connectionId}...`);
            
            const oldConnection = this.connections.get(connectionId);
            if (!oldConnection) return;
            
            // Create new client
            const newClient = this.createTradingViewClient();
            const newConnection = {
                client: newClient,
                category: oldConnection.category,
                symbols: new Set(),
                lastActivity: Date.now(),
                errorCount: 0
            };
            
            // Update connection
            this.connections.set(connectionId, newConnection);
            
            // Resubscribe to symbols
            const symbolsToResubscribe = Array.from(oldConnection.symbols);
            for (const symbol of symbolsToResubscribe) {
                // Remove old subscription
                this.subscriptions.delete(symbol);
                
                // Add to queue for resubscription
                setTimeout(() => {
                    this.subscribeToSymbol(symbol, oldConnection.category);
                }, 1000);
            }
            
            // Update health status
            this.connectionHealth.set(connectionId, {
                status: 'healthy',
                lastCheck: Date.now(),
                successRate: 100,
                avgResponseTime: 0
            });
            
            this.metrics.reconnections++;
            this.logger.info(`✅ Reconnected ${connectionId} successfully`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to reconnect ${connectionId}: ${error.message}`);
        }
    }
    
    startHealthMonitoring() {
        setInterval(() => {
            this.performHealthCheck();
        }, this.config.healthCheckInterval);
    }
    
    async performHealthCheck() {
        try {
            for (const [connectionId, connection] of this.connections.entries()) {
                const timeSinceActivity = Date.now() - connection.lastActivity;
                const health = this.connectionHealth.get(connectionId);
                
                if (timeSinceActivity > 60000) { // 1 minute without activity
                    health.status = 'stale';
                    this.logger.warn(`⚠️ Connection ${connectionId} appears stale`);
                } else if (connection.errorCount > 3) {
                    health.status = 'degraded';
                } else {
                    health.status = 'healthy';
                }
                
                health.lastCheck = Date.now();
            }
            
            this.logger.debug('🏥 Health check completed');
            
        } catch (error) {
            this.logger.error(`❌ Health check error: ${error.message}`);
        }
    }
    
    startMetricsReporting() {
        setInterval(() => {
            this.reportMetrics();
        }, 60000); // Every minute
    }
    
    async reportMetrics() {
        try {
            const uptime = Date.now() - this.metrics.startTime;
            const healthyConnections = Array.from(this.connectionHealth.values())
                .filter(h => h.status === 'healthy').length;
            
            const metricsReport = {
                ...this.metrics,
                uptime: Math.floor(uptime / 1000),
                healthyConnections,
                totalConnections: this.connections.size,
                subscriptionsCount: this.subscriptions.size,
                timestamp: new Date().toISOString()
            };
            
            // Publish metrics to Redis
            if (this.redisManager) {
                await this.redisManager.publishMetrics('forex_collector', metricsReport);
            }
            
            this.logger.info(`📊 Metrics: ${JSON.stringify(metricsReport)}`);
            
        } catch (error) {
            this.logger.error(`❌ Metrics reporting error: ${error.message}`);
        }
    }
    
    async subscribeToAllForexPairs() {
        try {
            this.logger.info('🌍 Subscribing to all forex pairs...');
            
            for (const [category, symbols] of Object.entries(this.symbolCategories)) {
                for (const symbol of symbols) {
                    await this.subscribeToSymbol(symbol, category);
                    
                    // Rate limiting delay
                    await new Promise(resolve => 
                        setTimeout(resolve, this.config.rateLimitDelay)
                    );
                }
            }
            
            this.logger.info(`✅ Subscribed to ${this.metrics.totalSymbols} forex pairs`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to subscribe to all forex pairs: ${error.message}`);
            throw error;
        }
    }
    
    getStatus() {
        return {
            isRunning: this.isRunning,
            metrics: this.metrics,
            connections: Object.fromEntries(
                Array.from(this.connections.entries()).map(([id, conn]) => [
                    id, 
                    {
                        category: conn.category,
                        symbolCount: conn.symbols.size,
                        errorCount: conn.errorCount,
                        lastActivity: conn.lastActivity
                    }
                ])
            ),
            health: Object.fromEntries(this.connectionHealth),
            subscriptions: this.subscriptions.size
        };
    }
    
    async shutdown() {
        try {
            this.logger.info('🛑 Shutting down Forex Collector...');
            
            this.isRunning = false;
            
            // Close all subscriptions
            for (const [symbol, subscription] of this.subscriptions.entries()) {
                try {
                    subscription.market.delete();
                    subscription.quoteSession.delete();
                } catch (error) {
                    this.logger.warn(`⚠️ Error closing subscription for ${symbol}: ${error.message}`);
                }
            }
            
            this.subscriptions.clear();
            this.connections.clear();
            
            if (this.redisManager) {
                await this.redisManager.disconnect();
            }
            
            this.logger.info('✅ Forex Collector shutdown complete');
            
        } catch (error) {
            this.logger.error(`❌ Shutdown error: ${error.message}`);
        }
    }
}

module.exports = ForexCollector;

// If running directly
if (require.main === module) {
    const collector = new ForexCollector({
        redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
        collectorId: process.env.COLLECTOR_ID || 'forex_collector_1'
    });
    
    collector.initialize()
        .then(() => collector.subscribeToAllForexPairs())
        .catch(error => {
            console.error('Failed to start Forex Collector:', error);
            process.exit(1);
        });
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        collector.shutdown().then(() => process.exit(0));
    });
    
    process.on('SIGTERM', () => {
        collector.shutdown().then(() => process.exit(0));
    });
}
