# Loki Configuration for TradingView API Enterprise
# Optimized for financial trading system log aggregation

auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096
  log_level: info

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://alertmanager:9093

# Limits configuration for financial data
limits_config:
  # Ingestion limits
  ingestion_rate_mb: 50
  ingestion_burst_size_mb: 100
  max_streams_per_user: 10000
  max_line_size: 256000
  
  # Query limits
  max_query_parallelism: 32
  max_query_series: 10000
  max_query_lookback: 720h  # 30 days
  
  # Retention
  retention_period: 2160h  # 90 days for financial compliance
  
  # Per-stream limits
  per_stream_rate_limit: 10MB
  per_stream_rate_limit_burst: 20MB
  
  # Cardinality limits
  max_global_streams_per_user: 5000
  max_chunks_per_query: 2000000
  max_concurrent_tail_requests: 10

# Table manager for retention
table_manager:
  retention_deletes_enabled: true
  retention_period: 2160h  # 90 days

# Compactor for chunk management
compactor:
  working_directory: /loki/compactor
  shared_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 2h
  retention_delete_worker_count: 150

# Analytics for usage tracking
analytics:
  reporting_enabled: false

# Tracing configuration
tracing:
  enabled: false
