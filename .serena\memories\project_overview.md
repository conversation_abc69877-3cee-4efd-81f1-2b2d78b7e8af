# Project Overview

## Purpose
This is a **Prop Firm API Server** that provides real-time market data and trading operations endpoints for proprietary trading firms. It acts as an intermediary between prop firm systems and TradingView's market data.

## Architecture
The system uses a **microservices architecture** combining:
- **FastAPI (Python)**: Main API server providing REST/WebSocket endpoints
- **Node.js Service**: TradingView-API wrapper for market data access  
- **TradingView-API**: Unofficial API library (Mathieu2301/TradingView-API)

## Key Features
- Real-time market data streaming via WebSocket
- Historical OHLCV data via REST endpoints
- Market symbol search functionality
- Trading operations (placeholder endpoints for future brokerage integration)
- API key-based authentication
- Interactive documentation via FastAPI Swagger UI

## Tech Stack
- **Primary Language**: Python 3.8+
- **Web Framework**: FastAPI
- **WebSockets**: websockets library
- **HTTP Client**: httpx
- **Data Validation**: Pydantic
- **Testing**: pytest (Python), Vitest (JavaScript)
- **Node.js Runtime**: 14+
- **Node.js Dependencies**: Express, Socket.io, ws, axios, dotenv

## Project Structure
- `api_server.py` - Main FastAPI application
- `node_service.js` - Basic Node.js TradingView service
- `node_service_robust.js` - Enhanced Node.js service with error handling
- `start_server.py` - Convenience startup script
- `src/` - TradingView-API source code (client, protocol handlers, sessions)
- `tests/` - Test files (both Python and JavaScript)
- `.clinerules/` - Project coding standards and guidelines