# Multi-stage Dockerfile for Enterprise Webhook Service
# Optimized for production with security best practices

# Build stage
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# Add metadata
LABEL maintainer="TradingView API Team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="tradingview-webhook-service" \
      org.label-schema.description="Enterprise webhook service for TradingView API" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.schema-version="1.0"

# Set environment variables for build
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Install system dependencies for building
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry==1.7.1

# Set work directory
WORKDIR /app

# Create minimal pyproject.toml for webhook service
COPY <<EOF ./pyproject.toml
[tool.poetry]
name = "webhook-service"
version = "1.0.0"
description = "Enterprise webhook service"
authors = ["TradingView API Team"]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
aiohttp = "^3.9.0"
aioredis = "^2.0.1"
pydantic = "^2.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
EOF

# Configure poetry and install dependencies
RUN poetry config virtualenvs.create true && \
    poetry config virtualenvs.in-project true && \
    poetry install --only=main --no-root && \
    rm -rf $POETRY_CACHE_DIR

# Production stage
FROM python:3.11-slim as production

# Set production environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH="/app" \
    ENV=production

# Create non-root user for security
RUN groupadd -r webhookuser && useradd -r -g webhookuser webhookuser

# Install runtime dependencies only
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set work directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder /app/.venv /app/.venv

# Copy application code
COPY enterprise_webhook_service.py ./

# Create webhook service startup script
COPY <<EOF ./start-webhook-service.py
#!/usr/bin/env python3

import os
import uvicorn
from enterprise_webhook_service import app

if __name__ == "__main__":
    port = int(os.getenv("SERVICE_PORT", 8000))
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    
    uvicorn.run(
        "enterprise_webhook_service:app",
        host=host,
        port=port,
        workers=1,
        loop="uvloop",
        http="httptools",
        access_log=True,
        log_level="info"
    )
EOF

# Make startup script executable
RUN chmod +x ./start-webhook-service.py

# Create necessary directories
RUN mkdir -p /app/logs /app/data && \
    chown -R webhookuser:webhookuser /app

# Switch to non-root user
USER webhookuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Default command
CMD ["python", "start-webhook-service.py"]
