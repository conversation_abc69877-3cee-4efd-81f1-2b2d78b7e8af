# **PLANNING.md**

## **Project: Prop Firm API Endpoint Server**

### **1\. Purpose**

To build a robust, low-latency API endpoint server for a proprietary trading firm. This server will act as an intermediary, fetching real-time market data (prices, bids, asks) and enabling trading operations by interacting with an unofficial TradingView API. It will provide a standardized interface for the firm's internal trading systems and charting applications.

### **2\. High-Level Vision**

Enable the prop firm to:

* Display live, per-second market data on custom charts.  
* Execute trading orders (market, limit, stop) programmatically.  
* Monitor account summary, open positions, pending orders, and trade history in real-time.  
* Maintain a clear separation between the trading logic and the external data source.

### **3\. Architecture Overview**

The system will consist of a Python-based API server leveraging FastAPI for REST endpoints and WebSocket for real-time data streaming.

* **Client-Side (Prop Firm's Systems):** Will connect to this API server to request data and send trading commands.  
* **API Server (This Project):**  
  * **FastAPI:** Handles REST API requests for historical data, trading actions (place, cancel, modify orders; close positions), and account/portfolio queries.  
  * **WebSocket:** Provides a continuous stream of real-time market data (prices, bids, asks).  
  * **Unofficial TradingView API Integration:** The core logic will interact with the unofficial Mathieu2301/TradingView-API to fetch raw data.  
  * **Data Processing/Normalization:** Raw data from the unofficial API will be processed, cleaned, and normalized into a consistent format before being served to the firm's systems.  
  * **Authentication/Authorization:** Secure access to all endpoints.  
* **Data Flow:**  
  * Real-time data: Unofficial TV API \-\> API Server (WebSocket) \-\> Client.  
  * Historical data: Unofficial TV API \-\> API Server (REST) \-\> Client.  
  * Trading actions: Client \-\> API Server (REST) \-\> Unofficial TV API (if applicable for order execution, otherwise to an internal trading engine).

### **4\. Tech Stack**

* **Primary Language:** Python 3.x  
* **Web Framework:** FastAPI  
* **Asynchronous Operations:** asyncio (inherent with FastAPI)  
* **HTTP Requests:** httpx or requests (for interacting with the unofficial TV API)  
* **WebSockets:** websockets library (for handling real-time data streaming)  
* **Data Validation:** pydantic (integrated with FastAPI)  
* **Testing:** pytest  
* **Dependency Management:** pip / requirements.txt  
* **Deployment (Future):** Docker

### **5\. Key Constraints & Risks**

* **Reliance on Unofficial API:**  
  * **Instability:** The Mathieu2301/TradingView-API is unofficial and may break without warning if TradingView changes its internal structures. This is the primary risk for data continuity.  
  * **Terms of Service:** Using an unofficial API might violate TradingView's terms of service, leading to potential IP blocks or legal issues.  
  * **Scalability/Rate Limits:** The unofficial API might not handle high request volumes required by a prop firm, or it might impose hidden rate limits.  
  * **Accuracy/Latency:** Data accuracy and guaranteed low latency cannot be assured.  
* **Security:** Handling sensitive trading operations requires robust authentication, authorization, and protection against common web vulnerabilities.  
* **Error Handling:** Comprehensive error handling for external API failures and internal processing issues is critical.  
* **Performance:** Need to monitor and optimize latency, especially for real-time data streams, to ensure competitive trading.

### **6\. Tools & Environments**

* **AI Coding Assistant:** Cline (with context7 and brave web search MCP tools enabled).  
* **Development Environment:** Python 3.x, pip.  
* **Version Control:** Git.

### **7\. Future Considerations**

* Integration with an actual brokerage/exchange API for live trading execution (moving beyond the unofficial TradingView API for execution, if that's the current plan).  
* Advanced logging and monitoring.  
* Scalability improvements (e.g., message queues for real-time data, distributed architecture).  
* Comprehensive error alerting.