# TradingView API Enterprise - Production Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
ENV=production
DEBUG=false
LOG_LEVEL=info

# API Configuration
API_KEYS=prod-key-123,prod-key-456,enterprise-key-789
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# =============================================================================
# TRADINGVIEW CREDENTIALS
# =============================================================================
# Get these from your TradingView account
TRADINGVIEW_SESSION=your_session_token_here
TRADINGVIEW_SIGNATURE=your_signature_here

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URLS=redis://redis-master:6379,redis://redis-replica:6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0
REDIS_MAX_CONNECTIONS=100
REDIS_TIMEOUT=30

# =============================================================================
# DATABASE CONFIGURATION (if using PostgreSQL)
# =============================================================================
# DATABASE_URL=********************************************/tradingview_api
# DB_POOL_SIZE=20
# DB_MAX_OVERFLOW=30
# DB_POOL_TIMEOUT=30

# =============================================================================
# COLLECTOR CONFIGURATION
# =============================================================================
# Forex Collector
FOREX_COLLECTOR_ID=forex_collector_prod_1
FOREX_RATE_LIMIT_DELAY=150
FOREX_MAX_CONNECTIONS=10

# Crypto Collector
CRYPTO_COLLECTOR_ID=crypto_collector_prod_1
CRYPTO_RATE_LIMIT_DELAY=100
CRYPTO_MAX_CONNECTIONS=15
CRYPTO_VOLUME_SPIKE_THRESHOLD=2.0

# Indices Collector
INDICES_COLLECTOR_ID=indices_collector_prod_1
INDICES_RATE_LIMIT_DELAY=200
INDICES_MAX_CONNECTIONS=8

# Commodities Collector
COMMODITIES_COLLECTOR_ID=commodities_collector_prod_1
COMMODITIES_RATE_LIMIT_DELAY=250
COMMODITIES_MAX_CONNECTIONS=6

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
ENABLE_WEBHOOKS=true
WEBHOOK_MAX_RETRIES=3
WEBHOOK_TIMEOUT=10
WEBHOOK_RATE_LIMIT=100
WEBHOOK_CIRCUIT_BREAKER_THRESHOLD=5
WEBHOOK_CIRCUIT_BREAKER_TIMEOUT=60

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration (if using JWT)
JWT_SECRET_KEY=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# CORS Configuration
CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=1000
RATE_LIMIT_BURST=100

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
SSL_PROTOCOLS=TLSv1.2,TLSv1.3

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Prometheus
PROMETHEUS_METRICS_ENABLED=true
PROMETHEUS_METRICS_PORT=8000
PROMETHEUS_METRICS_PATH=/metrics

# Grafana
GRAFANA_PASSWORD=your_secure_grafana_password_here
GRAFANA_ADMIN_USER=admin

# Alerting
ALERTMANAGER_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FORMAT=json
LOG_FILE_PATH=/app/logs/tradingview-api.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10
LOG_ROTATION=daily

# Structured logging
ENABLE_STRUCTURED_LOGGING=true
LOG_CORRELATION_ID=true
LOG_REQUEST_ID=true

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Python/FastAPI
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
UVICORN_WORKERS=1
UVICORN_LOOP=uvloop
UVICORN_HTTP=httptools

# Node.js
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=2048
UV_THREADPOOL_SIZE=4

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=tradingview-api-enterprise
COMPOSE_FILE=docker-compose.enterprise.yml

# Build arguments
BUILD_DATE=2024-01-15T10:30:00Z
VERSION=2.0.0
VCS_REF=main

# =============================================================================
# NETWORKING CONFIGURATION
# =============================================================================
DOCKER_NETWORK_SUBNET=172.20.0.0/16
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=4096

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=tradingview-api-backups
BACKUP_S3_REGION=us-east-1

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email service (for alerts)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password_here
SMTP_USE_TLS=true

# Slack integration (for alerts)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#trading-alerts

# AWS Configuration (if using AWS services)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_DEFAULT_REGION=us-east-1

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_MARKET_HOURS_FILTERING=true
ENABLE_VOLUME_SPIKE_DETECTION=true
ENABLE_SEASONAL_ANALYSIS=true
ENABLE_CORRELATION_TRACKING=true
ENABLE_CIRCUIT_BREAKERS=true
ENABLE_DEAD_LETTER_QUEUE=true

# =============================================================================
# COMPLIANCE AND AUDIT
# =============================================================================
ENABLE_AUDIT_LOGGING=true
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years for financial compliance
ENABLE_DATA_ENCRYPTION=true
ENABLE_PII_MASKING=true

# =============================================================================
# DEVELOPMENT OVERRIDES (remove in production)
# =============================================================================
# These should be removed or set to false in production
ENABLE_DEBUG_ENDPOINTS=false
ENABLE_SWAGGER_UI=true
ENABLE_REDOC=true
ALLOW_ORIGINS_REGEX=false
