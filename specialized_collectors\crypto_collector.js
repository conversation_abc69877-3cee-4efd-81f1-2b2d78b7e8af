/**
 * 🪙 CRYPTO COLLECTOR - Enterprise WebSocket Service
 * 
 * Specialized collector for cryptocurrency symbols with optimized rate limiting
 * and connection management specifically for digital assets.
 * 
 * Features:
 * - Dedicated to CRYPTO symbols only
 * - Optimized rate limiting for cryptocurrency pairs
 * - Session management for major/alt/defi coins
 * - Redis integration for enterprise data flow
 * - Volume spike detection for crypto volatility
 */

const TradingView = require('../main');
const { EnterpriseRedisManager } = require('./enterprise_redis_client');
const EventEmitter = require('events');

class CryptoCollector extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            maxConnections: 15, // Higher for crypto volatility
            rateLimitDelay: 100, // 100ms between requests (crypto-optimized)
            reconnectDelay: 3000, // Faster reconnect for crypto
            healthCheckInterval: 20000, // More frequent health checks
            redisUrl: config.redisUrl || 'redis://localhost:6379',
            collectorId: config.collectorId || 'crypto_collector_1',
            volumeSpikeThreshold: 2.0, // 200% volume spike detection
            ...config
        };
        
        // Crypto-specific symbol categories
        this.symbolCategories = {
            major: [
                'BINANCE:BTCUSDT', 'BINANCE:ETHUSDT', 'BINANCE:BNBUSDT', 
                'BINANCE:ADAUSDT', 'BINANCE:XRPUSDT', 'BINANCE:SOLUSDT',
                'BINANCE:DOTUSDT', 'BINANCE:DOGEUSDT'
            ],
            defi: [
                'BINANCE:UNIUSDT', 'BINANCE:LINKUSDT', 'BINANCE:AAVEUSDT',
                'BINANCE:COMPUSDT', 'BINANCE:MKRUSDT', 'BINANCE:SUSHIUSDT'
            ],
            layer1: [
                'BINANCE:AVAXUSDT', 'BINANCE:NEARUSDT', 'BINANCE:FTMUSDT',
                'BINANCE:ATOMUSDT', 'BINANCE:ALGOUSDT', 'BINANCE:EGLDUSDT'
            ],
            meme: [
                'BINANCE:DOGEUSDT', 'BINANCE:SHIBUSDT', 'BINANCE:PEPEUSDT',
                'BINANCE:FLOKIUSDT', 'BINANCE:BONKUSDT'
            ],
            stablecoin: [
                'BINANCE:USDCUSDT', 'BINANCE:BUSDUSDT', 'BINANCE:DAIUSDT',
                'BINANCE:TUSDUSDT', 'BINANCE:USDPUSDT'
            ]
        };
        
        // Connection management
        this.connections = new Map();
        this.subscriptions = new Map();
        this.connectionHealth = new Map();
        this.isRunning = false;
        
        // Crypto-specific features
        this.volumeHistory = new Map(); // Track volume for spike detection
        this.priceAlerts = new Map(); // Price movement alerts
        this.liquidationLevels = new Map(); // Track potential liquidation levels
        
        // Rate limiting
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.lastRequestTime = 0;
        
        // Redis integration
        this.redisManager = null;
        
        // Performance metrics
        this.metrics = {
            totalSymbols: 0,
            activeConnections: 0,
            messagesPublished: 0,
            volumeSpikes: 0,
            priceAlerts: 0,
            errors: 0,
            reconnections: 0,
            startTime: Date.now()
        };
        
        this.logger = {
            info: (msg) => console.log(`[CRYPTO-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            error: (msg) => console.error(`[CRYPTO-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            warn: (msg) => console.warn(`[CRYPTO-COLLECTOR] ${new Date().toISOString()} - ${msg}`),
            debug: (msg) => console.log(`[CRYPTO-COLLECTOR-DEBUG] ${new Date().toISOString()} - ${msg}`)
        };
    }
    
    async initialize() {
        try {
            this.logger.info('🚀 Initializing Crypto Collector...');
            
            // Initialize Redis connection
            this.redisManager = new EnterpriseRedisManager({
                redisUrl: this.config.redisUrl,
                collectorId: this.config.collectorId
            });
            
            await this.redisManager.initialize();
            
            // Create initial connections for different crypto categories
            await this.createCategoryConnections();
            
            // Start background tasks
            this.startHealthMonitoring();
            this.startMetricsReporting();
            this.startVolumeAnalysis();
            
            this.isRunning = true;
            this.logger.info('✅ Crypto Collector initialized successfully');
            
            return true;
            
        } catch (error) {
            this.logger.error(`❌ Failed to initialize Crypto Collector: ${error.message}`);
            throw error;
        }
    }
    
    async createCategoryConnections() {
        const categories = Object.keys(this.symbolCategories);
        
        for (const category of categories) {
            try {
                const connectionId = `crypto_${category}`;
                const client = this.createTradingViewClient();
                
                this.connections.set(connectionId, {
                    client,
                    category,
                    symbols: new Set(),
                    lastActivity: Date.now(),
                    errorCount: 0,
                    specialization: this.getCategorySpecialization(category)
                });
                
                this.connectionHealth.set(connectionId, {
                    status: 'healthy',
                    lastCheck: Date.now(),
                    successRate: 100,
                    avgResponseTime: 0,
                    category
                });
                
                this.logger.info(`🔗 Created connection for ${category} crypto: ${connectionId}`);
                
            } catch (error) {
                this.logger.error(`❌ Failed to create connection for ${category}: ${error.message}`);
            }
        }
        
        this.metrics.activeConnections = this.connections.size;
    }
    
    getCategorySpecialization(category) {
        const specializations = {
            major: { priority: 'high', alertThreshold: 0.05 }, // 5% price moves
            defi: { priority: 'high', alertThreshold: 0.10 }, // 10% price moves
            layer1: { priority: 'medium', alertThreshold: 0.08 }, // 8% price moves
            meme: { priority: 'medium', alertThreshold: 0.15 }, // 15% price moves (more volatile)
            stablecoin: { priority: 'low', alertThreshold: 0.01 } // 1% price moves (should be stable)
        };
        
        return specializations[category] || { priority: 'medium', alertThreshold: 0.10 };
    }
    
    createTradingViewClient() {
        const session = process.env.TRADINGVIEW_SESSION;
        const signature = process.env.TRADINGVIEW_SIGNATURE;
        
        if (session && signature) {
            return new TradingView.Client({
                token: session,
                signature: signature
            });
        } else {
            return new TradingView.Client();
        }
    }
    
    async subscribeToSymbol(symbol, category = null) {
        try {
            // Auto-detect category if not provided
            if (!category) {
                category = this.detectSymbolCategory(symbol);
            }
            
            const connectionId = `crypto_${category}`;
            const connection = this.connections.get(connectionId);
            
            if (!connection) {
                throw new Error(`No connection available for category: ${category}`);
            }
            
            // Check if already subscribed
            if (this.subscriptions.has(symbol)) {
                this.logger.warn(`⚠️ Already subscribed to ${symbol}`);
                return;
            }
            
            const client = connection.client;
            const quoteSession = new client.Session.Quote({ fields: 'all' });
            const market = new quoteSession.Market(symbol);
            
            // Set up real-time data handler
            market.onData((quoteData) => {
                this.handleMarketData(symbol, quoteData, connectionId, category);
            });
            
            market.onLoaded(() => {
                this.logger.info(`✅ Market loaded: ${symbol} (${category})`);
            });
            
            market.onError((...errorArgs) => {
                const errorMessage = errorArgs.join(' ');
                this.logger.error(`❌ Market error for ${symbol}: ${errorMessage}`);
                this.handleConnectionError(connectionId, symbol, errorMessage);
            });
            
            // Track subscription
            this.subscriptions.set(symbol, {
                market,
                quoteSession,
                connectionId,
                category,
                subscribedAt: Date.now(),
                specialization: connection.specialization
            });
            
            connection.symbols.add(symbol);
            connection.lastActivity = Date.now();
            
            // Initialize volume tracking
            this.volumeHistory.set(symbol, []);
            
            this.metrics.totalSymbols++;
            this.logger.info(`🔔 Subscribed to ${symbol} via ${connectionId}`);
            
        } catch (error) {
            this.logger.error(`💥 Failed to subscribe to ${symbol}: ${error.message}`);
            this.metrics.errors++;
            throw error;
        }
    }
    
    detectSymbolCategory(symbol) {
        for (const [category, symbols] of Object.entries(this.symbolCategories)) {
            if (symbols.includes(symbol)) {
                return category;
            }
        }
        
        // Default to major if not found
        return 'major';
    }
    
    async handleMarketData(symbol, quoteData, connectionId, category) {
        try {
            const currentPrice = quoteData.lp || null;
            const currentVolume = quoteData.volume || null;
            const connection = this.connections.get(connectionId);
            const specialization = connection?.specialization || {};
            
            // Calculate additional crypto metrics
            const spread = quoteData.ask && quoteData.bid ? (quoteData.ask - quoteData.bid) : null;
            const spreadPercent = spread && currentPrice ? (spread / currentPrice) * 100 : null;
            
            const marketData = {
                symbol,
                price: currentPrice,
                bid: quoteData.bid || null,
                ask: quoteData.ask || null,
                spread,
                spreadPercent,
                change: quoteData.ch || null,
                changePercent: quoteData.chp || null,
                volume: currentVolume,
                timestamp: new Date().toISOString(),
                source: 'crypto_collector',
                connectionId,
                category,
                priority: specialization.priority || 'medium',
                // Crypto-specific fields
                volatility: Math.abs(quoteData.chp || 0),
                isVolatile: Math.abs(quoteData.chp || 0) > (specialization.alertThreshold * 100),
                liquidationRisk: this.calculateLiquidationRisk(symbol, currentPrice)
            };
            
            // Volume spike detection
            if (currentVolume) {
                const volumeSpike = await this.detectVolumeSpike(symbol, currentVolume);
                if (volumeSpike) {
                    marketData.volumeSpike = volumeSpike;
                    this.metrics.volumeSpikes++;
                    
                    // Send volume spike alert
                    await this.sendVolumeAlert(symbol, volumeSpike, marketData);
                }
            }
            
            // Price alert detection
            if (marketData.isVolatile) {
                this.metrics.priceAlerts++;
                await this.sendPriceAlert(symbol, marketData);
            }
            
            // Publish to Redis
            if (this.redisManager) {
                await this.redisManager.publishMarketData(marketData);
            }
            
            // Emit event for local listeners
            this.emit('marketData', marketData);
            
            this.metrics.messagesPublished++;
            this.logger.debug(`📡 Published ${symbol}: ${currentPrice} (${category})`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to handle market data for ${symbol}: ${error.message}`);
            this.metrics.errors++;
        }
    }
    
    async detectVolumeSpike(symbol, currentVolume) {
        try {
            const history = this.volumeHistory.get(symbol) || [];
            
            // Add current volume to history
            history.push({
                volume: currentVolume,
                timestamp: Date.now()
            });
            
            // Keep only last 20 data points (about 10 minutes of data)
            if (history.length > 20) {
                history.shift();
            }
            
            this.volumeHistory.set(symbol, history);
            
            // Need at least 10 data points for analysis
            if (history.length < 10) {
                return null;
            }
            
            // Calculate average volume (excluding current)
            const previousVolumes = history.slice(0, -1).map(h => h.volume);
            const avgVolume = previousVolumes.reduce((a, b) => a + b, 0) / previousVolumes.length;
            
            // Check for spike
            const spikeRatio = currentVolume / avgVolume;
            
            if (spikeRatio >= this.config.volumeSpikeThreshold) {
                return {
                    ratio: spikeRatio,
                    currentVolume,
                    avgVolume,
                    severity: spikeRatio >= 5 ? 'extreme' : spikeRatio >= 3 ? 'high' : 'moderate'
                };
            }
            
            return null;
            
        } catch (error) {
            this.logger.error(`❌ Volume spike detection error for ${symbol}: ${error.message}`);
            return null;
        }
    }
    
    calculateLiquidationRisk(symbol, currentPrice) {
        // Simplified liquidation risk calculation
        // In production, this would use more sophisticated models
        const subscription = this.subscriptions.get(symbol);
        if (!subscription) return 'unknown';
        
        const category = subscription.category;
        const volatility = Math.abs(subscription.market?.data?.chp || 0);
        
        if (category === 'meme' && volatility > 15) return 'high';
        if (category === 'major' && volatility > 10) return 'medium';
        if (category === 'stablecoin' && volatility > 2) return 'high'; // Stablecoins shouldn't be volatile
        
        return volatility > 5 ? 'medium' : 'low';
    }
    
    async sendVolumeAlert(symbol, volumeSpike, marketData) {
        try {
            const alert = {
                type: 'volume_spike',
                symbol,
                severity: volumeSpike.severity,
                ratio: volumeSpike.ratio,
                currentVolume: volumeSpike.currentVolume,
                avgVolume: volumeSpike.avgVolume,
                price: marketData.price,
                timestamp: marketData.timestamp,
                source: 'crypto_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('volume_spike', alert);
            }
            
            this.logger.info(`🚨 Volume spike alert: ${symbol} - ${volumeSpike.ratio.toFixed(2)}x normal volume`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send volume alert for ${symbol}: ${error.message}`);
        }
    }
    
    async sendPriceAlert(symbol, marketData) {
        try {
            const alert = {
                type: 'price_movement',
                symbol,
                price: marketData.price,
                change: marketData.change,
                changePercent: marketData.changePercent,
                category: marketData.category,
                volatility: marketData.volatility,
                liquidationRisk: marketData.liquidationRisk,
                timestamp: marketData.timestamp,
                source: 'crypto_collector'
            };
            
            // Publish alert to Redis
            if (this.redisManager) {
                await this.redisManager.publishAlert('price_movement', alert);
            }
            
            this.logger.info(`📈 Price alert: ${symbol} - ${marketData.changePercent}% change`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to send price alert for ${symbol}: ${error.message}`);
        }
    }
    
    startVolumeAnalysis() {
        // Run volume analysis every 30 seconds
        setInterval(() => {
            this.performVolumeAnalysis();
        }, 30000);
    }
    
    async performVolumeAnalysis() {
        try {
            const analysis = {
                totalSymbols: this.volumeHistory.size,
                volumeSpikes: 0,
                categories: {}
            };
            
            for (const [symbol, history] of this.volumeHistory.entries()) {
                const subscription = this.subscriptions.get(symbol);
                if (!subscription) continue;
                
                const category = subscription.category;
                if (!analysis.categories[category]) {
                    analysis.categories[category] = { symbols: 0, spikes: 0 };
                }
                
                analysis.categories[category].symbols++;
                
                // Check recent volume activity
                const recentData = history.slice(-5); // Last 5 data points
                const hasRecentSpike = recentData.some(data => {
                    const avgVolume = history.slice(0, -1).reduce((a, b) => a + b.volume, 0) / (history.length - 1);
                    return data.volume / avgVolume >= this.config.volumeSpikeThreshold;
                });
                
                if (hasRecentSpike) {
                    analysis.categories[category].spikes++;
                    analysis.volumeSpikes++;
                }
            }
            
            // Store analysis in Redis
            if (this.redisManager) {
                await this.redisManager.storeAnalysis('crypto_volume_analysis', analysis);
            }
            
            this.logger.debug(`📊 Volume analysis: ${analysis.volumeSpikes} spikes across ${analysis.totalSymbols} symbols`);
            
        } catch (error) {
            this.logger.error(`❌ Volume analysis error: ${error.message}`);
        }
    }
    
    async subscribeToAllCryptoSymbols() {
        try {
            this.logger.info('🪙 Subscribing to all crypto symbols...');
            
            for (const [category, symbols] of Object.entries(this.symbolCategories)) {
                for (const symbol of symbols) {
                    await this.subscribeToSymbol(symbol, category);
                    
                    // Rate limiting delay (shorter for crypto due to higher volatility)
                    await new Promise(resolve => 
                        setTimeout(resolve, this.config.rateLimitDelay)
                    );
                }
            }
            
            this.logger.info(`✅ Subscribed to ${this.metrics.totalSymbols} crypto symbols`);
            
        } catch (error) {
            this.logger.error(`❌ Failed to subscribe to all crypto symbols: ${error.message}`);
            throw error;
        }
    }
    
    // ... (rest of the methods are similar to forex_collector.js but with crypto-specific optimizations)
    
    getStatus() {
        return {
            isRunning: this.isRunning,
            metrics: this.metrics,
            connections: Object.fromEntries(
                Array.from(this.connections.entries()).map(([id, conn]) => [
                    id, 
                    {
                        category: conn.category,
                        symbolCount: conn.symbols.size,
                        errorCount: conn.errorCount,
                        lastActivity: conn.lastActivity,
                        specialization: conn.specialization
                    }
                ])
            ),
            health: Object.fromEntries(this.connectionHealth),
            subscriptions: this.subscriptions.size,
            volumeTracking: this.volumeHistory.size,
            priceAlerts: this.priceAlerts.size
        };
    }
    
    async shutdown() {
        try {
            this.logger.info('🛑 Shutting down Crypto Collector...');
            
            this.isRunning = false;
            
            // Close all subscriptions
            for (const [symbol, subscription] of this.subscriptions.entries()) {
                try {
                    subscription.market.delete();
                    subscription.quoteSession.delete();
                } catch (error) {
                    this.logger.warn(`⚠️ Error closing subscription for ${symbol}: ${error.message}`);
                }
            }
            
            this.subscriptions.clear();
            this.connections.clear();
            this.volumeHistory.clear();
            this.priceAlerts.clear();
            
            if (this.redisManager) {
                await this.redisManager.disconnect();
            }
            
            this.logger.info('✅ Crypto Collector shutdown complete');
            
        } catch (error) {
            this.logger.error(`❌ Shutdown error: ${error.message}`);
        }
    }
}

module.exports = CryptoCollector;

// If running directly
if (require.main === module) {
    const collector = new CryptoCollector({
        redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
        collectorId: process.env.COLLECTOR_ID || 'crypto_collector_1'
    });
    
    collector.initialize()
        .then(() => collector.subscribeToAllCryptoSymbols())
        .catch(error => {
            console.error('Failed to start Crypto Collector:', error);
            process.exit(1);
        });
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        collector.shutdown().then(() => process.exit(0));
    });
    
    process.on('SIGTERM', () => {
        collector.shutdown().then(() => process.exit(0));
    });
}
