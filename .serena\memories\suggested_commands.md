# Suggested Development Commands

## Starting Services

### Complete System (Recommended)
```bash
python start_server.py
```

### Manual Startup
**Terminal 1 - Node.js Service (Robust):**
```bash
node node_service_robust.js
```

**Terminal 2 - FastAPI Server:**
```bash
uvicorn api_server:app --reload --host 0.0.0.0 --port 8000
```

### Development Mode
**Node.js:**
```bash
npm run dev  # Uses nodemon for auto-restart
```

**Python:**
```bash
uvicorn api_server:app --reload --host 0.0.0.0 --port 8000
```

## Testing

### Python Tests
```bash
pytest tests/
pytest tests/test_api.py -v  # Specific test file
```

### JavaScript Tests  
```bash
npx vitest
npx vitest tests/simpleChart.test.ts  # Specific test
```

### Manual API Testing
```bash
# Health check
curl http://localhost:8000/health
curl http://localhost:3001/health

# Market data (with auth)
curl -H "Authorization: Bearer your-api-key" http://localhost:8000/api/marketdata/quote/BINANCE:BTCUSDT
```

## Installation

### Python Dependencies
```bash
pip install -r requirements.txt
```

### Node.js Dependencies
```bash
npm install
```

### Environment Setup
```bash
cp .env.sample .env
# Edit .env with your configuration
```

## Utility Commands (Linux WSL2)

### File Operations
```bash
ls -la        # List files with details
find . -name "*.py" -type f  # Find Python files
grep -r "pattern" src/       # Search in source code
```

### Git Operations
```bash
git status
git add .
git commit -m "message"
```

### Process Management
```bash
ps aux | grep python    # Find Python processes
kill -9 <pid>          # Kill process by ID
netstat -tulpn | grep :8000  # Check port usage
```