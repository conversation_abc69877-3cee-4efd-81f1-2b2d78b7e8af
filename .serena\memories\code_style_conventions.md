# Code Style & Conventions

## Python Standards (Primary Language)

### General
- Follow **PEP8** coding standards
- Use **type hints** for all function parameters and return values
- Format code with **black** (if configured)
- Use **Pydantic** for data validation
- Keep files under **500 lines** - refactor into modules if exceeded

### Documentation
- Write **Google-style docstrings** for every function:
```python
def example(param1: str, param2: int) -> str:
    """
    Brief summary.

    Args:
        param1 (str): Description.
        param2 (int): Description.

    Returns:
        str: Description.
    """
```

### FastAPI Patterns
- Use **FastAPI** for REST APIs
- Use **Pydantic models** for request/response validation
- Implement proper **authentication middleware**
- Use **async/await** for I/O operations

### Code Structure
- Organize code into **clearly separated modules**
- Use **relative imports** within packages
- Group by feature or responsibility
- Never create files longer than 500 lines

## JavaScript/Node.js Standards

### ESLint Configuration
- Uses **Airbnb base** configuration
- **No console warnings** (allowed for server logging)
- Supports **CommonJS** and **ES2021**
- Allows **no-await-in-loop** for async operations

### Key Rules
- Use **async/await** for asynchronous operations
- Handle WebSocket connections gracefully
- Implement proper error handling and cleanup
- Use **Express** for HTTP server setup

## Testing Standards

### Python (pytest)
- Create tests in `/tests` folder mirroring app structure
- Include minimum tests per feature:
  - 1 test for expected use
  - 1 edge case test  
  - 1 failure case test
- Use **pytest-asyncio** for async testing
- Mock external dependencies appropriately

### JavaScript (Vitest)
- Configure timeout: 10000ms, retry: 3 attempts
- Use **dotenv** for environment setup
- Test client connections, chart sessions, indicators
- Use `tests/utils.ts` for common testing utilities

## Security Best Practices
- Never commit API keys, tokens, or credentials
- Use environment variables for sensitive configuration
- Implement comprehensive error handling
- Log errors without exposing sensitive data
- Handle rate limits and connection failures gracefully