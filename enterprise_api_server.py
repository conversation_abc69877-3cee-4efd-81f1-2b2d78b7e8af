"""
Enterprise FastAPI Server for TradingView API
Multi-instance ready with Redis clustering and webhook integration
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import uvicorn

from enterprise_redis_manager import EnterpriseRedisManager, MarketDataMessage, WebhookMessage
from enterprise_webhook_service import EnterpriseWebhookService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
API_KEYS = set(os.getenv("API_KEYS", "test-key-123,prod-key-456").split(","))
REDIS_URLS = os.getenv("REDIS_URLS", "redis://localhost:6379").split(",")
INSTANCE_ID = os.getenv("INSTANCE_ID", f"api_server_{int(time.time())}")
ENABLE_WEBHOOKS = os.getenv("ENABLE_WEBHOOKS", "true").lower() == "true"

# Global managers
redis_manager: Optional[EnterpriseRedisManager] = None
webhook_service: Optional[EnterpriseWebhookService] = None

# Request/Response Models
class MarketDataRequest(BaseModel):
    symbols: List[str] = Field(..., example=["EURUSD", "BTCUSD"])
    include_extended: bool = Field(False, description="Include extended market data")

class WebhookRegistration(BaseModel):
    endpoint_id: str = Field(..., example="trading_platform_1")
    url: str = Field(..., example="https://api.tradingplatform.com/webhooks/market-data")
    secret: Optional[str] = Field(None, description="Webhook signature secret")
    symbols: List[str] = Field(..., example=["EURUSD", "BTCUSD"])
    triggers: List[str] = Field(["price_change"], example=["price_change", "volume_spike"])

class SubscriptionRequest(BaseModel):
    symbols: List[str] = Field(..., example=["EURUSD", "BTCUSD"])
    webhook_endpoint: Optional[str] = Field(None, description="Webhook endpoint ID for notifications")

# WebSocket Connection Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.symbol_subscriptions: Dict[str, set] = {}  # symbol -> set of connection_ids
        self.connection_symbols: Dict[str, set] = {}    # connection_id -> set of symbols
        
    async def connect(self, websocket: WebSocket, connection_id: str):
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_symbols[connection_id] = set()
        logger.info(f"🔗 WebSocket connected: {connection_id}")
        
    def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            # Clean up subscriptions
            symbols = self.connection_symbols.get(connection_id, set())
            for symbol in symbols:
                if symbol in self.symbol_subscriptions:
                    self.symbol_subscriptions[symbol].discard(connection_id)
                    if not self.symbol_subscriptions[symbol]:
                        del self.symbol_subscriptions[symbol]
            
            del self.active_connections[connection_id]
            del self.connection_symbols[connection_id]
            logger.info(f"🔌 WebSocket disconnected: {connection_id}")
    
    async def subscribe_to_symbol(self, connection_id: str, symbol: str):
        if symbol not in self.symbol_subscriptions:
            self.symbol_subscriptions[symbol] = set()
        
        self.symbol_subscriptions[symbol].add(connection_id)
        self.connection_symbols[connection_id].add(symbol)
        
        # Subscribe to Redis updates for this symbol
        if redis_manager:
            await redis_manager.subscribe_to_symbol(symbol, self.broadcast_to_symbol_subscribers)
        
        logger.info(f"📡 Subscribed {connection_id} to {symbol}")
    
    async def broadcast_to_symbol_subscribers(self, market_data: MarketDataMessage):
        """Broadcast market data to all subscribers of a symbol"""
        symbol = market_data.symbol
        if symbol not in self.symbol_subscriptions:
            return
        
        message = {
            "type": "market_data",
            "symbol": symbol,
            "data": {
                "price": market_data.price,
                "bid": market_data.bid,
                "ask": market_data.ask,
                "volume": market_data.volume,
                "change": market_data.change,
                "change_percent": market_data.change_percent,
                "timestamp": market_data.timestamp
            }
        }
        
        # Send to all subscribers
        disconnected = []
        for connection_id in self.symbol_subscriptions[symbol]:
            if connection_id in self.active_connections:
                try:
                    websocket = self.active_connections[connection_id]
                    await websocket.send_json(message)
                except:
                    disconnected.append(connection_id)
        
        # Clean up disconnected clients
        for connection_id in disconnected:
            self.disconnect(connection_id)

# Initialize connection manager
connection_manager = ConnectionManager()

# Security
security = HTTPBearer()

async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.credentials not in API_KEYS:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

# Application lifecycle
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global redis_manager, webhook_service
    
    logger.info(f"🚀 Starting Enterprise API Server - Instance: {INSTANCE_ID}")
    
    # Initialize Redis Manager
    redis_manager = EnterpriseRedisManager(
        redis_urls=REDIS_URLS,
        cluster_mode=len(REDIS_URLS) > 1
    )
    
    redis_success = await redis_manager.initialize()
    if not redis_success:
        logger.error("❌ Failed to initialize Redis - continuing without Redis")
    
    # Initialize Webhook Service
    if ENABLE_WEBHOOKS:
        webhook_service = EnterpriseWebhookService(REDIS_URLS[0])
        webhook_success = await webhook_service.initialize()
        if not webhook_success:
            logger.error("❌ Failed to initialize Webhook Service")
    
    logger.info("✅ Enterprise API Server startup complete")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Enterprise API Server...")
    
    if webhook_service:
        await webhook_service.shutdown()
    
    if redis_manager:
        # Redis manager doesn't have explicit shutdown in our implementation
        pass
    
    logger.info("✅ Enterprise API Server shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="Enterprise TradingView API Server",
    description="""
    Enterprise-grade API server for real-time market data and trading operations.
    
    ## Features
    - **Multi-instance deployment** with Redis clustering
    - **Real-time WebSocket streaming** with automatic failover
    - **Enterprise webhook system** with retry logic and circuit breakers
    - **Horizontal scaling** with load balancer support
    - **Comprehensive monitoring** and health checks
    
    ## Architecture
    - **Specialized collectors** for different asset classes
    - **Redis Streams** for event sourcing and replay capability
    - **Circuit breakers** and rate limiting for reliability
    - **Dead letter queues** for failed webhook deliveries
    """,
    version="2.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint (no auth required)
@app.get("/health")
async def health_check():
    """Comprehensive health check for load balancer"""
    redis_status = "unknown"
    webhook_status = "disabled"
    
    if redis_manager:
        redis_health = await redis_manager.get_health_status()
        redis_status = redis_health.get("status", "unknown")
    
    if webhook_service:
        webhook_status_data = webhook_service.get_status()
        webhook_status = "healthy" if webhook_status_data["is_running"] else "unhealthy"
    
    overall_status = "healthy"
    if redis_status == "unhealthy" or webhook_status == "unhealthy":
        overall_status = "degraded"
    
    return {
        "status": overall_status,
        "instance_id": INSTANCE_ID,
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "redis": redis_status,
            "webhooks": webhook_status
        },
        "connections": {
            "websocket_connections": len(connection_manager.active_connections),
            "symbol_subscriptions": len(connection_manager.symbol_subscriptions)
        }
    }

# Market data endpoints
@app.get("/api/market-data/batch", tags=["Market Data"])
async def get_batch_market_data(
    request: MarketDataRequest,
    api_key: str = Depends(verify_api_key)
):
    """Get market data for multiple symbols"""
    if not redis_manager:
        raise HTTPException(status_code=503, detail="Redis not available")
    
    results = {}
    for symbol in request.symbols:
        market_data = await redis_manager.get_market_data(symbol)
        if market_data:
            results[symbol] = {
                "price": market_data.price,
                "bid": market_data.bid,
                "ask": market_data.ask,
                "volume": market_data.volume,
                "change": market_data.change,
                "change_percent": market_data.change_percent,
                "timestamp": market_data.timestamp,
                "source": market_data.source
            }
        else:
            results[symbol] = None
    
    return {
        "data": results,
        "timestamp": datetime.utcnow().isoformat(),
        "instance_id": INSTANCE_ID
    }

@app.get("/api/market-data/{symbol}", tags=["Market Data"])
async def get_symbol_market_data(
    symbol: str,
    api_key: str = Depends(verify_api_key)
):
    """Get market data for a single symbol"""
    if not redis_manager:
        raise HTTPException(status_code=503, detail="Redis not available")
    
    market_data = await redis_manager.get_market_data(symbol)
    if not market_data:
        raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
    
    return {
        "symbol": symbol,
        "price": market_data.price,
        "bid": market_data.bid,
        "ask": market_data.ask,
        "volume": market_data.volume,
        "change": market_data.change,
        "change_percent": market_data.change_percent,
        "timestamp": market_data.timestamp,
        "source": market_data.source,
        "instance_id": INSTANCE_ID
    }

# Webhook endpoints
@app.post("/api/webhooks/register", tags=["Webhooks"])
async def register_webhook(
    registration: WebhookRegistration,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """Register a webhook endpoint for market data notifications"""
    if not webhook_service:
        raise HTTPException(status_code=503, detail="Webhook service not available")
    
    from enterprise_webhook_service import WebhookEndpoint, WebhookPriority
    
    endpoint = WebhookEndpoint(
        id=registration.endpoint_id,
        url=registration.url,
        secret=registration.secret,
        priority=WebhookPriority.NORMAL
    )
    
    success = await webhook_service.register_endpoint(endpoint)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to register webhook")
    
    return {
        "status": "registered",
        "endpoint_id": registration.endpoint_id,
        "symbols": registration.symbols,
        "triggers": registration.triggers
    }

@app.post("/api/webhooks/test", tags=["Webhooks"])
async def test_webhook(
    endpoint_id: str,
    test_payload: dict = None,
    api_key: str = Depends(verify_api_key)
):
    """Send a test webhook"""
    if not webhook_service:
        raise HTTPException(status_code=503, detail="Webhook service not available")
    
    if not test_payload:
        test_payload = {
            "type": "test",
            "message": "Test webhook from Enterprise API Server",
            "timestamp": datetime.utcnow().isoformat(),
            "instance_id": INSTANCE_ID
        }
    
    from enterprise_webhook_service import WebhookPriority
    
    try:
        delivery_id = await webhook_service.queue_webhook(
            endpoint_id, 
            test_payload, 
            WebhookPriority.HIGH
        )
        return {
            "status": "queued",
            "delivery_id": delivery_id,
            "endpoint_id": endpoint_id
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# WebSocket endpoint
@app.websocket("/ws/{connection_id}")
async def websocket_endpoint(websocket: WebSocket, connection_id: str):
    """WebSocket endpoint for real-time market data"""
    await connection_manager.connect(websocket, connection_id)
    
    try:
        while True:
            # Receive subscription requests
            data = await websocket.receive_json()
            
            if data.get("action") == "subscribe":
                symbols = data.get("symbols", [])
                for symbol in symbols:
                    await connection_manager.subscribe_to_symbol(connection_id, symbol)
                
                await websocket.send_json({
                    "type": "subscription_confirmed",
                    "symbols": symbols,
                    "connection_id": connection_id
                })
            
            elif data.get("action") == "ping":
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.utcnow().isoformat()
                })
                
    except WebSocketDisconnect:
        connection_manager.disconnect(connection_id)
    except Exception as e:
        logger.error(f"❌ WebSocket error for {connection_id}: {e}")
        connection_manager.disconnect(connection_id)

# System monitoring endpoints
@app.get("/api/system/status", tags=["System"])
async def get_system_status(api_key: str = Depends(verify_api_key)):
    """Get comprehensive system status"""
    redis_health = None
    webhook_status = None
    
    if redis_manager:
        redis_health = await redis_manager.get_health_status()
    
    if webhook_service:
        webhook_status = webhook_service.get_status()
    
    return {
        "instance_id": INSTANCE_ID,
        "timestamp": datetime.utcnow().isoformat(),
        "redis": redis_health,
        "webhooks": webhook_status,
        "websockets": {
            "active_connections": len(connection_manager.active_connections),
            "symbol_subscriptions": len(connection_manager.symbol_subscriptions),
            "total_subscriptions": sum(
                len(subs) for subs in connection_manager.symbol_subscriptions.values()
            )
        }
    }

@app.get("/api/system/metrics", tags=["System"])
async def get_system_metrics(api_key: str = Depends(verify_api_key)):
    """Get system performance metrics"""
    metrics = {
        "instance_id": INSTANCE_ID,
        "timestamp": datetime.utcnow().isoformat(),
        "websocket_connections": len(connection_manager.active_connections),
        "symbol_subscriptions": len(connection_manager.symbol_subscriptions)
    }
    
    if redis_manager:
        redis_health = await redis_manager.get_health_status()
        if "metrics" in redis_health:
            metrics["redis"] = redis_health["metrics"]
    
    if webhook_service:
        webhook_status = webhook_service.get_status()
        metrics["webhooks"] = webhook_status["metrics"]
    
    return metrics

if __name__ == "__main__":
    port = int(os.getenv("API_PORT", 8001))
    
    uvicorn.run(
        "enterprise_api_server:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        workers=1  # Use 1 worker per instance, scale with multiple instances
    )
