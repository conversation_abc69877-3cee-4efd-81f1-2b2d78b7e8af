# Grafana Datasource Configuration for TradingView API Enterprise
# Configures Prometheus as the primary metrics datasource

apiVersion: 1

datasources:
  # Primary Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      queryTimeout: 60s
      timeInterval: 15s
      # Custom headers for authentication (if needed)
      # httpHeaderName1: 'Authorization'
    # secureJsonData:
    #   httpHeaderValue1: 'Bearer your-token-here'
    
  # Loki for logs (if using Loki)
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      timeout: 60s
      
  # Redis datasource (if using Redis plugin)
  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://redis-master:6379
    editable: true
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    # secureJsonData:
    #   password: 'your-redis-password'

  # InfluxDB datasource (if using for time-series data)
  # - name: InfluxDB
  #   type: influxdb
  #   access: proxy
  #   url: http://influxdb:8086
  #   database: tradingview_metrics
  #   user: admin
  #   editable: true
  #   jsonData:
  #     timeInterval: "15s"
  #     httpMode: GET
  #   secureJsonData:
  #     password: 'your-influxdb-password'

  # PostgreSQL datasource (if using for business metrics)
  # - name: PostgreSQL
  #   type: postgres
  #   access: proxy
  #   url: postgres:5432
  #   database: tradingview_business
  #   user: grafana_reader
  #   editable: true
  #   jsonData:
  #     sslmode: disable
  #     maxOpenConns: 0
  #     maxIdleConns: 2
  #     connMaxLifetime: 14400
  #   secureJsonData:
  #     password: 'your-postgres-password'

  # TestData datasource for development/testing
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData:
      # Configuration for test data scenarios
      scenarios:
        - id: random_walk
          name: Random Walk
          stringInput: ""
        - id: csv_metric_values
          name: CSV Metric Values
          stringInput: "1,20,90,30,5,0"
