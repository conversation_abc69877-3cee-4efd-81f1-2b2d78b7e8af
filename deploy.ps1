# TradingView API Enterprise Deployment Script (PowerShell)
# Handles production deployment with zero-downtime rolling updates

param(
    [Parameter(Position=0)]
    [ValidateSet("deploy", "update", "rollback", "status", "logs", "backup", "restore", "health", "scale", "stop", "start", "restart", "clean")]
    [string]$Command,
    
    [Parameter()]
    [ValidateSet("production", "staging", "development")]
    [string]$Environment = "production",
    
    [Parameter()]
    [string]$Service = "",
    
    [Parameter()]
    [string]$Version = "latest",
    
    [Parameter()]
    [int]$Replicas = 0,
    
    [Parameter()]
    [switch]$Force,
    
    [Parameter()]
    [switch]$Help
)

# Script configuration
$ProjectName = "tradingview-api-enterprise"
$ComposeFile = "docker-compose.enterprise.yml"
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

# Help function
function Show-Help {
    @"
TradingView API Enterprise Deployment Script (PowerShell)

Usage: .\deploy.ps1 [COMMAND] [OPTIONS]

Commands:
    deploy          Deploy the entire stack
    update          Update services with zero downtime
    rollback        Rollback to previous version
    status          Show deployment status
    logs            Show service logs
    backup          Create backup before deployment
    restore         Restore from backup
    health          Check system health
    scale           Scale services
    stop            Stop all services
    start           Start all services
    restart         Restart all services
    clean           Clean up unused resources

Options:
    -Environment    Environment (production|staging|development)
    -Service        Specific service to operate on
    -Version        Version tag for deployment
    -Replicas       Number of replicas for scaling
    -Force          Force operation without confirmation
    -Help           Show this help message

Examples:
    .\deploy.ps1 deploy -Environment production
    .\deploy.ps1 update -Service api-server-1
    .\deploy.ps1 scale -Service api-server -Replicas 5
    .\deploy.ps1 logs -Service forex-collector
    .\deploy.ps1 health

"@
}

# Validate environment
function Test-Environment {
    param([string]$Env)
    
    Write-Info "Environment: $Env"
    
    # Set environment file
    $script:EnvFile = ".env.$Env"
    if (-not (Test-Path $script:EnvFile)) {
        Write-Error "Environment file not found: $script:EnvFile"
        exit 1
    }
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    try {
        $null = docker --version
    }
    catch {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    
    # Check Docker Compose
    try {
        $null = docker-compose --version
    }
    catch {
        Write-Error "Docker Compose is not installed or not in PATH"
        exit 1
    }
    
    # Check if Docker daemon is running
    try {
        $null = docker info 2>$null
    }
    catch {
        Write-Error "Docker daemon is not running"
        exit 1
    }
    
    # Check environment file
    if (-not (Test-Path $script:EnvFile)) {
        Write-Error "Environment file not found: $script:EnvFile"
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

# Create backup
function New-Backup {
    Write-Info "Creating backup..."
    
    $BackupDir = "backups\$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    
    # Backup Redis data
    $redisRunning = docker-compose -f $ComposeFile --env-file $script:EnvFile ps redis-master | Select-String "Up"
    if ($redisRunning) {
        Write-Info "Backing up Redis data..."
        docker-compose -f $ComposeFile --env-file $script:EnvFile exec -T redis-master redis-cli BGSAVE
        $containerId = docker-compose -f $ComposeFile --env-file $script:EnvFile ps -q redis-master
        docker cp "${containerId}:/data/dump.rdb" "$BackupDir\redis-dump.rdb"
    }
    
    # Backup configuration files
    Copy-Item -Path "monitoring" -Destination $BackupDir -Recurse -Force
    Copy-Item -Path $script:EnvFile -Destination $BackupDir -Force
    Copy-Item -Path $ComposeFile -Destination $BackupDir -Force
    
    # Backup logs
    if (Test-Path "logs") {
        Copy-Item -Path "logs" -Destination $BackupDir -Recurse -Force
    }
    
    Write-Success "Backup created: $BackupDir"
    $BackupDir | Out-File -FilePath ".last_backup" -Encoding UTF8
}

# Health check
function Test-Health {
    Write-Info "Performing health check..."
    
    $failedServices = @()
    $services = @("api-server-1", "api-server-2", "api-server-3", "forex-collector", "crypto-collector", "indices-collector", "commodities-collector", "webhook-service", "redis-master", "nginx-lb")
    
    foreach ($service in $services) {
        $serviceStatus = docker-compose -f $ComposeFile --env-file $script:EnvFile ps $service | Select-String "Up"
        
        if ($serviceStatus) {
            # Check health endpoint if available
            $healthUrl = switch -Wildcard ($service) {
                "api-server-*" { "http://localhost:8000/health" }
                "webhook-service" { "http://localhost:8000/health" }
                "*-collector" { "http://localhost:3001/health" }
                "nginx-lb" { "http://localhost:80/health" }
                default { $null }
            }
            
            if ($healthUrl) {
                try {
                    $response = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 5 -UseBasicParsing
                    if ($response.StatusCode -eq 200) {
                        Write-Success "$service is healthy"
                    } else {
                        $failedServices += $service
                        Write-Error "$service health check failed"
                    }
                }
                catch {
                    $failedServices += $service
                    Write-Error "$service is not responding"
                }
            } else {
                Write-Success "$service is running"
            }
        } else {
            $failedServices += $service
            Write-Error "$service is not running"
        }
    }
    
    if ($failedServices.Count -eq 0) {
        Write-Success "All services are healthy"
        return $true
    } else {
        Write-Error "Failed services: $($failedServices -join ', ')"
        return $false
    }
}

# Deploy function
function Start-Deployment {
    Write-Info "Starting deployment for environment: $Environment"
    
    # Create backup
    New-Backup
    
    # Pull latest images
    Write-Info "Pulling latest images..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile pull
    
    # Build custom images
    Write-Info "Building custom images..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile build
    
    # Start infrastructure services first
    Write-Info "Starting infrastructure services..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile up -d redis-master redis-replica prometheus grafana loki
    
    # Wait for Redis to be ready
    Write-Info "Waiting for Redis to be ready..."
    $timeout = 60
    $elapsed = 0
    do {
        Start-Sleep -Seconds 2
        $elapsed += 2
        $redisReady = docker-compose -f $ComposeFile --env-file $script:EnvFile exec redis-master redis-cli ping 2>$null | Select-String "PONG"
    } while (-not $redisReady -and $elapsed -lt $timeout)
    
    if (-not $redisReady) {
        Write-Error "Redis failed to start within timeout"
        exit 1
    }
    
    # Start collectors
    Write-Info "Starting collectors..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile up -d forex-collector crypto-collector indices-collector commodities-collector
    
    # Start API servers
    Write-Info "Starting API servers..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile up -d api-server-1 api-server-2 api-server-3
    
    # Start webhook service
    Write-Info "Starting webhook service..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile up -d webhook-service
    
    # Start load balancer last
    Write-Info "Starting load balancer..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile up -d nginx-lb
    
    # Wait for services to be ready
    Write-Info "Waiting for services to be ready..."
    Start-Sleep -Seconds 30
    
    # Perform health check
    if (Test-Health) {
        Write-Success "Deployment completed successfully!"
    } else {
        Write-Error "Deployment completed with errors. Check service logs."
        exit 1
    }
}

# Update function with zero downtime
function Start-Update {
    Write-Info "Performing zero-downtime update..."
    
    if ($Service) {
        Write-Info "Updating service: $Service"
        
        # Create backup
        New-Backup
        
        # Update specific service
        docker-compose -f $ComposeFile --env-file $script:EnvFile pull $Service
        docker-compose -f $ComposeFile --env-file $script:EnvFile up -d --no-deps $Service
        
        # Wait and health check
        Start-Sleep -Seconds 10
        if (Test-Health) {
            Write-Success "Service $Service updated successfully"
        } else {
            Write-Error "Service $Service update failed"
            exit 1
        }
    } else {
        # Rolling update of all services
        $services = @("api-server-1", "api-server-2", "api-server-3")
        
        foreach ($svc in $services) {
            Write-Info "Updating $svc..."
            docker-compose -f $ComposeFile --env-file $script:EnvFile pull $svc
            docker-compose -f $ComposeFile --env-file $script:EnvFile up -d --no-deps $svc
            Start-Sleep -Seconds 15  # Wait for service to be ready
            
            if (-not (Test-Health)) {
                Write-Error "Update failed for $svc"
                exit 1
            }
        }
        
        Write-Success "Rolling update completed successfully"
    }
}

# Scale function
function Start-Scale {
    if (-not $Service -or $Replicas -eq 0) {
        Write-Error "Service and replicas must be specified for scaling"
        exit 1
    }
    
    Write-Info "Scaling $Service to $Replicas replicas..."
    docker-compose -f $ComposeFile --env-file $script:EnvFile up -d --scale "$Service=$Replicas"
    
    Write-Success "Scaled $Service to $Replicas replicas"
}

# Show logs
function Show-Logs {
    if ($Service) {
        docker-compose -f $ComposeFile --env-file $script:EnvFile logs -f $Service
    } else {
        docker-compose -f $ComposeFile --env-file $script:EnvFile logs -f
    }
}

# Show status
function Show-Status {
    Write-Info "Service Status:"
    docker-compose -f $ComposeFile --env-file $script:EnvFile ps
    
    Write-Info "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Main execution
function Main {
    if ($Help -or -not $Command) {
        Show-Help
        return
    }
    
    Test-Environment -Env $Environment
    Test-Prerequisites
    
    switch ($Command) {
        "deploy" {
            if (-not $Force -and $Environment -eq "production") {
                $confirmation = Read-Host "Are you sure you want to deploy to production? (y/N)"
                if ($confirmation -ne "y" -and $confirmation -ne "Y") {
                    Write-Info "Deployment cancelled"
                    return
                }
            }
            Start-Deployment
        }
        "update" { Start-Update }
        "health" { Test-Health }
        "status" { Show-Status }
        "logs" { Show-Logs }
        "scale" { Start-Scale }
        "backup" { New-Backup }
        "stop" { docker-compose -f $ComposeFile --env-file $script:EnvFile stop }
        "start" { docker-compose -f $ComposeFile --env-file $script:EnvFile start }
        "restart" { docker-compose -f $ComposeFile --env-file $script:EnvFile restart }
        "clean" { 
            docker system prune -f
            docker volume prune -f
        }
        default {
            Write-Error "Unknown command: $Command"
            Show-Help
            exit 1
        }
    }
}

# Run main function
Main
